<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="SPLITTER_PROPORTION" value="0.37493128" />
    <option name="GROUP_BY_SEVERITY" value="true" />
    <option name="SCOPE_TYPE" value="8" />
    <option name="CUSTOM_SCOPE_NAME" value="Project Production Files" />
  </component>
  <component name="AndroidLayouts">
    <shared>
      <config>
        <device id="pixel_3_xl" />
        <device id="pixel_xl" />
        <device id="pixel_3" />
        <device id="Nexus 5X" />
        <device id="Nexus 6P" />
        <device id="pixel" />
        <device id="pixel_2" />
        <device id="tv_1080p" />
        <device id="Nexus 7 2013" />
        <device id="Nexus 7" />
        <locale>zh</locale>
      </config>
    </shared>
    <layouts>
      <layout url="file://$PROJECT_DIR$/Launcher/src/main/res/layout/activity_main.xml">
        <config>
          <theme>@style/Theme.YXService</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/Launcher/src/main/res/layout/custom_horizontal_scrollview.xml">
        <config>
          <theme>@style/Theme.YXService</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/Launcher/src/main/res/layout/item_grid.xml">
        <config>
          <theme>@style/Theme.YXService</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/Launcher/src/main/res/layout/page_gridview.xml">
        <config>
          <theme>@style/Theme.YXService</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/Launcher/src/main/res/layout/page_indicator_marker.xml">
        <config>
          <theme>@style/Theme.YXService</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/Launcher/src/main/res/layout/page_one.xml">
        <config>
          <theme>@style/Theme.YXService</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/Launcher/src/main/res/layout/reverse_video.xml">
        <config>
          <theme>@style/Theme.YXService</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/Launcher/src/main/res/layout/setting_common_dialog.xml">
        <config>
          <theme>@style/Theme.YXService</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/Launcher/src/main/res/layout/standby_activity.xml">
        <config>
          <theme>@style/Theme.YXService</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/Launcher/src/main/res/layout/widgets_bt.xml">
        <config>
          <theme>@style/Theme.YXService</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/Launcher/src/main/res/layout/widgets_bt_music.xml">
        <config>
          <theme>@style/Theme.YXService</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/Launcher/src/main/res/layout/widgets_music.xml">
        <config>
          <theme>@style/Theme.YXService</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/Launcher/src/main/res/layout/widgets_navi.xml">
        <config>
          <theme>@style/Theme.YXService</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/Launcher/src/main/res/layout/widgets_personal_setting.xml">
        <config>
          <theme>@style/Theme.YXService</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/Launcher/src/main/res/layout/widgets_radio.xml">
        <config>
          <theme>@style/Theme.YXService</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/Launcher/src/main/res/layout/yx_widgets_weather.xml">
        <config>
          <theme>@style/Theme.YXService</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/feature-weather/src/main/res/layout/yx_activity_weather.xml">
        <config>
          <theme>@android:style/Theme.Material.Light</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/feature-weather/src/main/res/layout/yx_item_daily_forecast.xml">
        <config>
          <theme>@android:style/Theme.Material.Light</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/feature-weather/src/main/res/layout/yx_item_hourly_forecast.xml">
        <config>
          <theme>@android:style/Theme.Material.Light</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/feature-weather/src/main/res/layout/yx_widget_weather.xml">
        <config>
          <theme>@android:style/Theme.Material.Light</theme>
        </config>
      </layout>
    </layouts>
  </component>
  <component name="AndroidLogFilters">
    <option name="TOOL_WINDOW_LOG_LEVEL" value="verbose" />
    <option name="TOOL_WINDOW_CONFIGURED_FILTER" value="Show only selected application" />
    <option name="TOOL_WINDOW_REGEXP_FILTER" value="false" />
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="BranchesTreeState">
    <expand>
      <path>
        <item name="ROOT" type="e8cecc67:BranchNodeDescriptor" />
        <item name="LOCAL_ROOT" type="e8cecc67:BranchNodeDescriptor" />
      </path>
      <path>
        <item name="ROOT" type="e8cecc67:BranchNodeDescriptor" />
        <item name="REMOTE_ROOT" type="e8cecc67:BranchNodeDescriptor" />
      </path>
      <path>
        <item name="ROOT" type="e8cecc67:BranchNodeDescriptor" />
        <item name="REMOTE_ROOT" type="e8cecc67:BranchNodeDescriptor" />
        <item name="GROUP_NODE:origin" type="e8cecc67:BranchNodeDescriptor" />
      </path>
    </expand>
    <select />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c52f9512-1994-499f-a2d1-081635403813" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/HvacSDK.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/consumer-rules.pro" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/proguard-rules.pro" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/readme" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/androidTest/java/com/yaxon/hvacsdk/ExampleInstrumentedTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/HvacConstant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/HvacSDKApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/callback/IHvacCallback.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/constant/ConstDef_Can.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/controller/BaseHvacController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/controller/DataStore.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/controller/HvacControllerFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/controller/YaXonController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/controller/imp/JacHvacController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/controller/imp/JacLEF20HvacController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/model/CanDataFrame.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/model/CanProtocolFrame.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/model/ProtocolFrame.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/mvvm/HvacContract.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/mvvm/HvacModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/mvvm/HvacViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/utils/DataConvert.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/voice/HvacVoiceCtrlManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/voice/HvacVoiceManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/main/java/com/yaxon/hvacsdk/voice/IManagerLifeCycle.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/HvacSDK/src/test/java/com/yaxon/hvacsdk/ExampleUnitTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Launcher/build.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/Launcher/build.gradle" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Launcher/src/main/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/Launcher/src/main/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Launcher/src/main/java/com/yaxon/launcher/main/App.java" beforeDir="false" afterPath="$PROJECT_DIR$/Launcher/src/main/java/com/yaxon/launcher/main/App.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Launcher/src/main/java/com/yaxon/launcher/main/MainActivity.java" beforeDir="false" afterPath="$PROJECT_DIR$/Launcher/src/main/java/com/yaxon/launcher/main/MainActivity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Launcher/src/main/java/com/yaxon/launcher/utils/Utils.java" beforeDir="false" afterPath="$PROJECT_DIR$/Launcher/src/main/java/com/yaxon/launcher/utils/Utils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Launcher/src/main/java/com/yaxon/launcher/view/WidgetsScrollView.java" beforeDir="false" afterPath="$PROJECT_DIR$/Launcher/src/main/java/com/yaxon/launcher/view/WidgetsScrollView.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Launcher/src/main/res/layout/activity_main.xml" beforeDir="false" afterPath="$PROJECT_DIR$/Launcher/src/main/res/layout/activity_main.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Launcher/src/main/res/layout/widgets_bt.xml" beforeDir="false" afterPath="$PROJECT_DIR$/Launcher/src/main/res/layout/widgets_bt.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Launcher/src/main/res/values/strings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/Launcher/src/main/res/values/strings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/libs/cropper-2.0.0.aar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/libs/lrc.aar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/androidTest/java/com/example/musicplay/MusicPlayActivityTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/assets/Rolling in the Deep-Adele.lrc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/MusicApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/model/LrcContent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/util/BroadcastUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/util/FileManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/util/IntentUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/util/MusicFileDataControl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/util/SystemSettingUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/util/SystemUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/util/ThreadManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/util/VolumeUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/musicSource/MusicTestActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/musicdetail/MusicDetailFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/musicdetail/MusicDetailViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/musiclist/MusicListAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/musiclist/MusicListFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/musiclist/MusicListOldAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/musiclist/MusicListViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/musiclist/collectfavor/MusicCollectFavorFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/musiclist/collectfavor/MusicCollectFavorListAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/musicplay/MusicFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/musicplay/MusicPlayActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/musicplay/MusicPlayViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/photolist/PhotoFolderFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/photolist/PhotoFolderViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/usbplay/PhotoFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/usbplay/UsbPlayFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/usbplay/UsbPlayViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/videolist/VideoFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/videolist/VideoListAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/videolist/VideoListFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/videolist/VideoListViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/view/videolist/VideoPlayViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/widget/BorderCircleImageView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/widget/CircleImageView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/widget/DiscView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/widget/LrcView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/widget/MarqueeText.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/widget/RectAnimView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/widget/RefreshingDialog.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/widget/VideoControlShowView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/widget/VideoView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/widget/gallery/LoopAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/widget/gallery/LoopPagerAdapterWrapper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/example/musicplay/widget/gallery/LoopViewPager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/yaxon/music/dialog/YXToast.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/yaxon/music/ui/MusicPlayerActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/yaxon/music/ui/UsbPlayerActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/yaxon/music/ui/VideoPlayerActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/yaxon/music/widget/MusicWidget.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/java/com/yaxon/music/widget/TopSmoothScroller.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/anim/loading_rotate_anim.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/brightness_x.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_back_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_favorlist_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_lrc_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_lrc_selector_gold.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_lrc_selector_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_musicsetting_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_next_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_next_selector_widget.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_next_x.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_openlist_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_pause_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_pause_selector_widget.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_pause_x.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_play_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_play_selector_widget.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_play_x.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_playmode_all_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_playmode_order_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_playmode_sequence_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_playmode_single_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_playmode_suffle_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_prev_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_prev_selector_widget.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_prev_x.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_repeatall_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_repeatmode_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/btn_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/collect_delete_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/drawable_anim.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/list_forum_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/menu_btn_next_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/menu_btn_prev_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/music_list_line.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/seekbar_img.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/seekbar_img_gold.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/seekbar_img_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/seekbar_style_gold.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/seekbar_style_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/seekbar_style_two.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/sound_x.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/tab_divider.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/title_bar_button_back_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/drawable/video_list.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/font/h_li_hei_regular.TTF" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/activity_music_play.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/activity_music_source.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/activity_usb_player.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/common_dialog.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/control_bar.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/control_bar_video.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/fragment_music_detail_zq.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/fragment_music_play_zq.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/fragment_musiclist_collectfavor.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/fragment_musiclist_zq.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/fragment_photo_folder.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/fragment_photo_list.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/fragment_usb_play.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/fragment_video_play.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/fragment_videolist.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/gallery_item.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/item_musiclist_collectfavor.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/item_musiclist_file.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/item_musiclist_zq.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/item_videolist.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/layout_discview.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/layout_refresh_dialog.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/layout_tab.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/music_app_widget.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/rect_anim_item.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/seek_bar.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/title_bar.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/layout/video_control_show_view.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/bg_progress_bar.9.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/bottom_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/brightness_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/brightness_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_bg_selected.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_music_widget_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_next_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_next_press.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_next_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_openlist_click.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_openlist_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_pause_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_pause_press.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_pause_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_play_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_play_press.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_play_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_playmode_all_click.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_playmode_all_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_playmode_random_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_playmode_random_press.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_playmode_sequence_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_playmode_sequence_press.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_playmode_single_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_playmode_single_press.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_prev_click.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_prev_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_prev_press.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_widget_music.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_widget_next_click.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_widget_next_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_widget_pause_click.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_widget_pause_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_widget_play_click.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_widget_play_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_widget_prev_click.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/btn_widget_prev_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/common_default_dialog_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/common_dialog_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/common_return_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/common_return_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/default_wallpaper_day.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/ic_bt_repeatall.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/ic_bt_repeatall_press.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/ic_circle.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/ic_disc1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/ic_loading.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/ic_needle1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/img_added_favor.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/img_favor_list.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/img_favor_list_press.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/img_music_collect_delete.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/img_music_collect_delete_press.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/img_music_setting.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/img_music_setting_press.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/img_picture_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/img_unadd_favor.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/iv_disc_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/iv_music_bottom.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/list_item_play.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/media_collect_list_img.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/media_error_video_default.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/media_lrc_btn_gold.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/media_lrc_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/media_lrc_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/media_lrc_btn_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/media_order_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/media_order_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/media_vedio_pause.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/media_video_brightness_img.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/media_video_default.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/media_video_volume_img.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/menu_btn_next_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/menu_btn_next_selected.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/menu_btn_pre_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/menu_btn_pre_selected.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/multimedia_playbar_seekbar_over.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/multimedia_playbar_seekbar_over_gold.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/multimedia_playbar_seekbar_over_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/multimedia_usb_card.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_album_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_file_img.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_icon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_icon_playing.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_pic.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_playing_1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_playing_10.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_playing_11.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_playing_12.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_playing_13.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_playing_14.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_playing_15.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_playing_16.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_playing_17.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_playing_2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_playing_3.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_playing_4.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_playing_5.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_playing_6.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_playing_7.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_playing_8.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_playing_9.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_song_img.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/music_widget_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/progress_bar.9.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/progress_bar_gold.9.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/progress_bar_red.9.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/sound_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/sound_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/thumb.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/title_bar_button_back_single.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/title_bar_button_back_single_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/video_list_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/mipmap-hdpi/video_list_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/values-zh/dimens_zq.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/values-zh/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/values-zh/strings_zq.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/values/attrs.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/values/colors.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/values/colors_zq.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/values/dimens.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/values/dimens_zq.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/values/strings_zq.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/values/styles.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/main/res/values/styles_zq.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Music/src/test/java/com/example/musicplay/ExampleUnitTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/ijkJniLibs/arm64-v8a/libijkffmpeg.so" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/ijkJniLibs/arm64-v8a/libijkplayer.so" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/ijkJniLibs/arm64-v8a/libijksdl.so" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/ijkJniLibs/armeabi-v7a/libijkffmpeg.so" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/ijkJniLibs/armeabi-v7a/libijkplayer.so" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/ijkJniLibs/armeabi-v7a/libijksdl.so" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/libs/jaudiotagger-2.2.6.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/libs/pinyin4j-2.5.0.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/aidl/com/yaxon/musicplayer/model/IMediaPlaybackService.aidl" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/aidl/com/yaxon/musicplayer/model/IMusicServiceListener.aidl" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/aidl/com/yaxon/musicplayer/model/IStatusService.aidl" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/aidl/com/yaxon/telematics/service/aidl/its/ItsAutoMusicInterface.aidl" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/YXApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/base/YXBaseActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/base/YXBaseFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/base/YXBaseRecyclerAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/broadcast/BluetoothCallBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/broadcast/BroadcastAcitonConstant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/broadcast/EscBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/broadcast/ExternalMountBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/broadcast/KeyEventBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/broadcast/MusicBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/broadcast/MusicWidgetBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/broadcast/ProjectKeyEventBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/broadcast/SDCardBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/broadcast/ScreenDownBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/broadcast/SystemCloseEventBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/broadcast/SystemSleepBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/broadcast/USBBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/broadcast/VoiceControlBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/broadcast/VoiceRecognizeBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/broadcast/VoiceSwitchControlBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/constant/ArgsKey.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/constant/CommandType.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/constant/Constant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/constant/MusicConstant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/constant/ProjectConstant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/constant/ProjectMusicConstant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/constant/SPKey.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/data/database/music/MusicDao.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/data/database/music/MusicDataBaseUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/data/database/music/MusicDatabase.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/data/model/FileDataModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/data/model/MusicAction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/data/model/MusicSetting.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/data/model/RawDataSourceProvider.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/data/model/SongModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/data/model/SongProvider.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/data/source/music/MusicRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/data/source/music/MusicSource.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/data/source/music/local/MusicLocalSource.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/data/source/music/remote/MusicRemoteSource.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/event/BaseEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/event/DeviceStatusEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/event/FactoryTestEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/event/MusicChangeEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/event/MusicReceiverActionEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/event/MusicViewChangeEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/event/manager/AudioChangeEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/event/manager/EventManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/event/manager/MusicEventManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/event/manager/ProjectEventManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/event/manager/ProjectMusicEventManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/interfaceclass/IMusicAction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/interfaceclass/IMusicInfoGet.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/interfaceclass/IMusicPlayAction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/interfaceclass/IMusicService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/interfaceclass/IMusicSetting.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/manager/DeviceManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/manager/MusicControlManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/manager/MusicDataManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/manager/MusicPlayManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/manager/MusicPlayManagerV2.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/mvvm/Event.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/mvvm/EventObserver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/service/MusicService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/task/DataReadTask.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/AppUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/BitmapHelper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/BroadcastUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/ClickHelper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/CrashExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/DBUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/DataItemListener.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/DataItemListenerI.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/DensityUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/MusicReadUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/MusicSdkUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/MusicToolUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/NullUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/PinyinUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/SharedPrefsManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/SharedPrefsTag.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/ShowUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/StringUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/TestHelper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/TimeUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/ToastUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/utils/UriUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/voice/IManagerLifeCycle.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/voice/MusicVoiceCtrlManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/voice/MusicVoiceManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/widget/DialogLoading.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/widget/ExoplayerControlView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/widget/ExoplayerView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/widget/MusicControlView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/widget/MusicView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/widget/RotateLoading.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/musicsdk/widget/dialog/YXToast.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/java/com/yaxon/telematics/service/aidl/its/ItsAutoMusicService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/anim/music_fragment_in.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/anim/music_fragment_out.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/anim/music_fragment_slide_in_right.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/anim/music_fragment_slide_out_right.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/drawable/base_toast_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/layout/activity_base_content.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/layout/base_layout_toast.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/layout/common_dialog.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/layout/dialog_loading.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/mipmap-hdpi/common_default_dialog_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/raw/aaaa.mp3" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/raw/bbbb.mp3" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/raw/track.mp3" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/values/attrs.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/values/colors.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/values/dimens.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/values/strings_tools.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/main/res/values/styles.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/test/java/com/yaxon/musicsdk/MusicUnitTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MusicSDK/src/test/java/com/yaxon/musicsdk/SDKExampleUnitTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/libs/cropper-2.0.0.aar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/androidTest/java/com/yaxon/photo/ExampleInstrumentedTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/java/com/yaxon/photo/PhotoApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/java/com/yaxon/photo/view/photo/PhotoActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/java/com/yaxon/photo/view/photobrowse/PhotoBrowseFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/java/com/yaxon/photo/view/photobrowse/PhotoBrowseViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/java/com/yaxon/photo/view/photobrowse/YXViewPager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/java/com/yaxon/photo/view/photofolder/PhotoFolderFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/java/com/yaxon/photo/view/photofolder/PhotoFolderViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable-v24/ic_launcher_foreground.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/btn_back_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/btn_photo_15s_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/btn_photo_3s_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/btn_photo_7s_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/btn_photo_last.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/btn_photo_list.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/btn_photo_next.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/btn_photo_next_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/btn_photo_pause_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/btn_photo_play_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/btn_photo_prev_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/btn_photo_rotate_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/btn_photo_zoom_in_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/btn_photo_zoom_out_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/btn_white_trans_radus_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/ic_launcher_background.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/white_trans_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/drawable/white_trans_bg_p.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/layout/activity_photo.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/layout/fragment_photo_browse.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/layout/fragment_photo_folder.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-anydpi-v26/ic_launcher.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-hdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-hdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-hdpi/setting_icon_wallpaper_4.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-hdpi/test_img.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_15s_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_15s_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_3s_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_3s_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_7s_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_7s_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_list_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_list_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_next_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_next_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_pause_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_pause_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_play_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_play_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_prev_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_prev_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_rotate_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_rotate_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_zoom_in_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_zoom_in_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_zoom_out_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/btn_photo_zoom_out_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/common_return_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/common_return_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/ic_controlbar_bg_low.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/main_background.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/media_picture_next_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/media_picture_next_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/media_picture_prev_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-mdpi/media_picture_prev_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-xhdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-xhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-xxhdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-xxhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-xxxhdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/values-night/themes.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/values/colors.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/values/dimens.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/values/styles.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/main/res/values/themes.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Photo/src/test/java/com/yaxon/photo/ExampleUnitTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/libs/glide-3.7.0.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/androidTest/java/com/yaxon/photo_sdk/ExampleInstrumentedTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/YXApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/base/YXBaseActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/base/YXBaseFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/broadcast/StorageBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/broadcast/VoiceRecognizeBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/constant/Constant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/constant/PhotoConstant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/constant/SPKey.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/data/database/photo/PhotoDao.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/data/database/photo/PhotoDataBaseUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/data/database/photo/PhotoDatabase.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/data/model/DateTypeConverter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/data/model/PhotoAction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/data/model/PhotoModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/data/model/PhotoProvider.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/data/model/PhotoSetting.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/event/BaseEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/event/DeviceStatusEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/event/PhotoChangeEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/event/PhotoReceiverActionEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/event/PhotoViewChangeEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/interfaceclass/IPhotoAction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/interfaceclass/IPhotoFunction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/interfaceclass/IPhotoInfoGet.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/interfaceclass/IPhotoPlayAction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/interfaceclass/IPhotoSetting.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/manager/PhotoControlManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/manager/PhotoDataManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/manager/PhotoFunctionManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/manager/PhotoPlayManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/mvvm/Event.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/mvvm/EventObserver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/utils/BitmapHelper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/utils/CrashExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/utils/FileInfo.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/utils/HandlerThreadUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/utils/NullUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/utils/PhotoSdkUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/utils/SharedPrefsManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/utils/StringUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/utils/ToastUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/utils/ViewUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/widget/DialogLoading.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/widget/PhotoBrowseAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/widget/PhotoFolderGridAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/widget/PhotoView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/widget/RotateGestureDetector.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/java/com/yaxon/photosdk/widget/RotateLoading.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/anim/photo_fragment_in.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/anim/photo_fragment_out.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/anim/photo_fragment_slide_in_right.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/anim/photo_fragment_slide_out_right.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/anim/push_up_in.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/drawable-v24/ic_launcher_foreground.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/drawable/base_toast_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/drawable/ic_launcher_background.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/layout/activity_base_content.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/layout/base_layout_toast.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/layout/dialog_loading.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/layout/photo_item.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/mipmap-anydpi-v26/ic_launcher.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/mipmap-hdpi/common_default_dialog_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/mipmap-hdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/mipmap-hdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/mipmap-mdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/mipmap-mdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/mipmap-mdpi/ic_photos_default_large.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/mipmap-mdpi/ic_photos_default_small.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/mipmap-mdpi/ic_photos_error_large.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/mipmap-mdpi/ic_photos_error_small.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/mipmap-xhdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/mipmap-xhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/mipmap-xxhdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/mipmap-xxhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/mipmap-xxxhdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/values-night/themes.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/values/attrs.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/values/colors.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/values/dimens.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/values/styles.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/main/res/values/themes.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PhotoSDK/src/test/java/com/yaxon/photo_sdk/ExampleUnitTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/consumer-rules.pro" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/libs/yx_support_classes.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/proguard-rules.pro" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/androidTest/java/com/yaxon/radiosdk/ExampleInstrumentedTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/assets/litepal.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/RadioSdkApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/bean/BandAm1Bean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/bean/BandAm2Bean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/bean/BandBaseBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/bean/BandFm1Bean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/bean/BandFm2Bean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/bean/BandFm3Bean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/bean/CollectionBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/bean/SearchBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/bean/ValidBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/constant/Constants.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/crash/CrashExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/iinterface/IManagerLifeCycle.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/iinterface/IRadioAction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/iinterface/IRadioCollectionAction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/iinterface/IRadioPlatformAction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/iinterface/IRadioPlayAction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/iinterface/IRadioTestAction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/iinterface/IRadioUpdateUIContract.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/manager/BandTypeManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/manager/PlatformBaseManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/manager/PlatformMTK2712Manager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/manager/RadioCollectionManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/manager/RadioControlManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/manager/RadioPlayManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/manager/RadioTestManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/manager/TimerManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/manager/datamanager/RadioDBHelper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/manager/datamanager/RadioSPManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/manager/datamanager/RadioSettingsManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/utils/HandlerBgUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/utils/LogUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/utils/RadioFreqFormatUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/voice/IVoiceCmdCallback.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/voice/IVoiceControl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/voice/RadioVoiceCmd.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/java/com/yaxon/radiosdk/voice/RadioVoiceManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/main/res/values/arrays.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RadioSDK/src/test/java/com/yaxon/radiosdk/ExampleUnitTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/readme" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/Android" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/ImageWallpaper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/SystemUISDKApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/SystemUIService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/UiOffloadThread.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/controller/AppActionStateController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/controller/AppActionStateControllerImp.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/controller/BaseController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/controller/BluetoothController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/controller/BluetoothControllerImp.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/controller/ControllerFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/controller/LocaleController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/controller/LocaleControllerImp.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/controller/StorageController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/controller/StorageControllerImp.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/controller/VolumeController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/controller/VolumeControllerImp.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/controller/WifiApController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/controller/WifiApControllerImp.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/controller/WifiController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/controller/WifiControllerImp.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/sdk/Configs.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/sdk/Constant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/sdk/manager/NaviBarManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/sdk/manager/PhoneBarManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/sdk/manager/PhoneBarSmallManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/sdk/manager/StatusBarManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/sdk/manager/VolumeDialogManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/sdk/statusbar/StatusServiceCallback.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/sdk/utils/ScreenShotUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/sdk/utils/SystemUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/sdk/utils/VolumeUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/sdk/view/ListenerBackPullDownContainer.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/sdk/view/ListenerPullDownContainer.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/sdk/view/PullLayout.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/sdk/view/qs/QsTileView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/java/com/android/systemui/sdk/view/qs/QsTileViewContainer.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/res/drawable/screenshot_panel.9.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/res/layout/global_screenshot_view.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/res/layout/qs_view.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/res/values/attrs.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/res/values/colors.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/res/values/config.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/res/values/dimens.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SystemUISDK/src/main/res/values/styles.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/androidTest/java/com/example/videoplay/MusicPlayActivityTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/java/com/example/videoplay/VideoApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/java/com/example/videoplay/model/LrcContent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/java/com/example/videoplay/util/IntentUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/java/com/example/videoplay/view/videolist/VideoListAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/java/com/example/videoplay/view/videolist/VideoListFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/java/com/example/videoplay/view/videolist/VideoListViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/java/com/example/videoplay/view/videolist/collectfavor/MusicCollectFavorFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/java/com/example/videoplay/view/videolist/collectfavor/MusicCollectFavorListAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/java/com/example/videoplay/view/videoplay/VideoFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/java/com/example/videoplay/view/videoplay/VideoPlayViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/java/com/example/videoplay/widget/BrightnessPopupWindow.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/java/com/example/videoplay/widget/CircleImageView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/java/com/example/videoplay/widget/LrcView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/java/com/example/videoplay/widget/RectAnimView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/java/com/example/videoplay/widget/VerticalSeekBar.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/java/com/example/videoplay/widget/VideoView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/java/com/yaxon/video/views/VideoActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/brightness_x.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/btn_next_x.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/btn_pause_x.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/btn_play_x.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/btn_playmode_sequence_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/btn_prev_x.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/btn_repeatall_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/btn_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/collect_delete_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/list_forum_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/menu_btn_next_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/menu_btn_prev_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/music_list_line.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/seekbar_img.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/seekbar_light_style.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/seekbar_thumb_y.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/sound_x.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/title_bar_button_back_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/drawable/video_list.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/font/h_li_hei_regular.TTF" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/layout/activity_music_source.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/layout/control_bar.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/layout/fragment_video_play.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/layout/fragment_videolist.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/layout/fragment_videolist_collectfavor.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/layout/item_videolist.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/layout/item_videolist_collectfavor.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/layout/rect_anim_item.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/layout/seek_bar.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/layout/seekbar_brightness.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/layout/title_bar.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/bg_progress_bar.9.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/bg_video.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/bottom_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/brightness_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/brightness_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/btn_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/btn_bg_selected.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/btn_next_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/btn_next_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/btn_pause_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/btn_pause_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/btn_play_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/btn_play_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/btn_playmode_sequence_click.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/btn_playmode_sequence_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/btn_prev_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/btn_prev_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/ic_bt_repeatall.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/ic_bt_repeatall_press.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/icon_video_list_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/icon_video_list_prssed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/img_added_favor.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/img_music_collect_delete.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/img_music_collect_delete_press.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/img_unadd_favor.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/list_item_play.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/menu_btn_next_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/menu_btn_next_selected.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/menu_btn_pre_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/menu_btn_pre_selected.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/music_icon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/music_pic.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/progress_bar.9.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/sound_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/sound_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/thumb.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/title_bar_button_back_single.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/title_bar_button_back_single_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/video_list_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/mipmap-mdpi/video_list_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/values-zh/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/values-zh/strings_zq.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/values/attrs.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/values/colors.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/values/colors_zq.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/values/dimens.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/values/dimens_zq.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/values/strings_zq.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/values/styles.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/main/res/values/styles_zq.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Video/src/test/java/com/example/videoplay/ExampleUnitTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/androidTest/java/com/yaxon/videosdk/utils/NullUtilTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/YXApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/base/YXBaseActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/base/YXBaseFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/base/YXBaseRecyclerAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/broadcast/BluetoothCallBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/broadcast/BroadcastAcitonConstant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/broadcast/EscBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/broadcast/ExternalMountBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/broadcast/KeyEventBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/broadcast/MusicBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/broadcast/MusicWidgetBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/broadcast/ProjectKeyEventBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/broadcast/SDCardBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/broadcast/ScreenDownBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/broadcast/SystemCloseEventBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/broadcast/SystemSleepBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/broadcast/USBBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/broadcast/VoiceControlBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/broadcast/VoiceRecognizeBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/broadcast/VoiceSwitchControlBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/constant/ArgsKey.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/constant/CommandType.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/constant/Constant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/constant/MusicConstant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/constant/ProjectConstant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/constant/ProjectMusicConstant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/constant/SPKey.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/constant/VideoConstant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/data/database/music/VideoDao.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/data/database/music/VideoDataBaseUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/data/database/music/VideoDatabase.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/data/model/VideoAction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/data/model/VideoModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/data/model/VideoProvider.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/data/model/VideoSetting.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/data/source/music/MusicRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/data/source/music/MusicSource.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/data/source/music/local/MusicLocalSource.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/data/source/music/remote/MusicRemoteSource.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/event/BaseEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/event/DeviceStatusEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/event/MusicReceiverActionEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/event/VideoChangeEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/event/VideoViewChangeEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/event/manager/EventManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/event/manager/MusicEventManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/event/manager/ProjectEventManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/event/manager/ProjectMusicEventManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/interfaceclass/IMusicAction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/interfaceclass/IMusicInfoGet.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/interfaceclass/IMusicPlayAction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/interfaceclass/IMusicPlaySuppport.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/interfaceclass/IMusicService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/interfaceclass/IMusicSetting.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/manager/AudioChangeEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/manager/DeviceManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/manager/VideoControlManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/manager/VideoDataManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/manager/VideoPlayManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/manager/VideoPlayManagerV2.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/manager/VideoPlayManagerV3.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/mvvm/Event.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/mvvm/EventObserver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/service/VideoService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/task/DataReadTask.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/AppUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/BitmapHelper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/BroadcastUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/CrashExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/DBUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/DataItemListener.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/DataItemListenerI.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/DensityUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/MusicReadUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/MusicToolUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/NullUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/SharedPrefsManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/SharedPrefsTag.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/ShowUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/StringUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/TimeUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/ToastUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/UriUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/utils/VideoSdkUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/widget/DialogLoading.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/widget/ExoplayerControlView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/widget/ExoplayerView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/widget/MusicControlView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/widget/RotateLoading.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/widget/VideoView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/java/com/yaxon/videosdk/widget/dialog/YXToast.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/res/anim/music_fragment_in.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/res/anim/music_fragment_out.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/res/anim/music_fragment_slide_in_right.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/res/anim/music_fragment_slide_out_right.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/res/drawable/base_toast_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/res/layout/activity_base_content.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/res/layout/base_layout_toast.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/res/layout/common_dialog.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/res/layout/dialog_loading.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/res/mipmap-hdpi/common_default_dialog_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/res/raw/video1.mp4" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/res/values/attrs.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/res/values/colors.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/res/values/strings_tools.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/main/res/values/styles.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/test/java/com/yaxon/videosdk/MusicUnitTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/VideoSDK/src/test/java/com/yaxon/videosdk/SDKExampleUnitTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/assets/litepal.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/androidTest/java/com/yaxon/bluetooth/ExampleInstrumentedTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/BluetoothApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/Constant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/adapter/BluetoothForumAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/adapter/BluetoothNearAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/adapter/CallRecordAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/adapter/PhoneBookAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/adapter/PhoneBookDetailAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/base/BaseBindingActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/base/BaseBindingFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/base/BaseBindingModelActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/bt/BluetoothStateBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/bt/IPairingDeviceListener.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/bt/PairingDeviceHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/bt/PhoneStateBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/fragment/BlueToothForumFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/fragment/BluetoothNameFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/fragment/BluetoothNearFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/fragment/KeyBoardFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/fragment/PhoneBookFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/fragment/PhoneRecordFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/fragment/RecordAllFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/fragment/RecordMissedFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/main/BluetoothMusicActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/main/BluetoothPhoneActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/main/BluetoothSettingActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/main/MainActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/main/NumberDetailActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/provider/BluetoothPhoneProvider.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/receiver/BootBroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/service/BluetoothService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/utils/DensityUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/utils/IntentUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/utils/KeyBoardUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/utils/ProgramLoaderUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/viewmodel/BluetoothPhoneViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/viewmodel/CallHistoryViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/viewmodel/ContactListViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/viewmodel/KeyBoardViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/viewmodel/MusicViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/viewmodel/PhoneBookDetailModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/viewmodel/SettingViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/views/CircleImageView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/views/ClearEditText.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/views/ListViewForScrollView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/views/MarqueeText.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/views/MarqueeTextView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/views/SlideBar.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/views/TabView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/views/YXDialog.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/views/YXOnClickListener.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/java/com/yaxon/bluetooth/views/YXToast.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/anim/keyboard_bottom_in.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/anim/keyboard_bottom_out.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/anim/loading_rotate_anim.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/color/bt_theme_blue_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/color/bt_theme_golden_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/color/bt_theme_red_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/color/common_tab_item_text_color.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/color/keep_call_blue_color.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/color/keep_call_golden_color.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/color/keep_call_red_color.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable-v24/ic_launcher_foreground.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/animation_music_album.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/bt_search_progressbar.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/btn_bt_dialog_cancel.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/btn_bt_dialog_confirm.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/btn_bt_open.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/btn_keyboard_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/btn_last_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/btn_next_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/btn_pause_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/btn_phone_record.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/btn_play_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/btn_pop_delete.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/btn_pop_dial.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/btn_pop_dismiss.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/common_back.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/common_tab_next_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/common_tab_pre_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/et_search_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/ic_category_golden_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/ic_category_red_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/ic_category_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/ic_confirm_btn_golden_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/ic_confirm_btn_red_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/ic_confirm_btn_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/ic_item_freq_blue_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/ic_item_freq_golden_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/ic_item_freq_red_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/ic_launcher_background.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/img_delete_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/iv_add_call.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/iv_hangup.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/iv_keep_call_blue.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/iv_keep_call_golden.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/iv_keep_call_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/iv_key_board.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/iv_key_broad_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/iv_mic_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/iv_pickup.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/iv_switch_phone_private.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/iv_switch_phone_public.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/progressbar_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/progressbar_circle.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/selector_btn_switch_blue.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/selector_btn_switch_golden.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/selector_btn_switch_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/selector_common_arrow_right.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/selector_common_default_cancel_btn.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/selector_common_default_confirm_btn.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/selector_common_default_confirm_btn_golden.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/selector_common_default_confirm_btn_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/selector_common_progressbar.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/selector_dialog_btn.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/selector_edittext_corner_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/selector_msg_choice_multiple.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/selector_progressbar.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/setting_more_btn.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/setting_more_refresh.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/tab_item_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/drawable/title_indicator.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/activity_bluetooth_phone.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/activity_bluetooth_setting.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/activity_main.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/activity_music_bluetooth.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/activity_number_detail.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/dialog_fragment_delete.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/dialog_fragment_options.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/fragment_blueforum.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/fragment_bluename.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/fragment_near.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/fragment_new_keyboard.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/fragment_phone_record.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/fragment_phonebook.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/fragment_record.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/listitem_phonebook.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/listitem_phonebook_detail.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/listitem_phonerecord.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/listview_item_forum.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/listview_item_near.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/open_bluetooth_dialog_view.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/popup_window.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/popupwindow_keyboard.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/setting_common_dialog.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/layout/yx_tabview.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_hangup_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_hangup_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_keep_call_d_blue.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_keep_call_n_white.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_keep_call_p_blue.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_keep_call_p_golden.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_keep_call_p_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_keyboard_n_yellow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_keyboard_p_yellow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_mini_d_yellow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_mini_n_yellow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_mini_p_yellow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_novoice_n_yellow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_novoice_p_blue.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_novoice_p_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_novoice_yellow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_pickup_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_pickup_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_transform_phone_n_yellow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_transform_phone_p_blue.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_transform_phone_p_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_button_transform_phone_yellow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_volume_golden.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_volume_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_volume_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_call_volume_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_calllogs_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_calllogs_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_contacs_list_tel_img.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_contact_center_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_contacts_delete_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_contacts_delete_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_contacts_detail_img.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_contacts_header_img0.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_contacts_header_img1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_contacts_header_img2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_contacts_header_img3.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_contacts_header_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_contacts_input_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_contacts_search_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_contacts_search_ico.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_dialing_keyboard_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_dialing_keyboard_c_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_dialing_keyboard_c_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_dialing_keyboard_d_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_dialing_keyboard_d_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_dialing_keyboard_hide_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_dialing_keyboard_hide_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_dialing_keyboard_line.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_dialing_keyboard_line_golden.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_dialing_keyboard_line_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_dialing_keyboard_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_dialing_keyboard_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_key_box.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_key_box_golden.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_key_box_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_list_in.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_list_miss.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_list_out.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_phone_dial_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_phone_dial_contacts_big_icon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_phone_dial_contacts_big_icon1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_phone_dial_mini_default_img.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_set_overlay_yellow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_setup_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_setup_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/bt_unconnect_img.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/category_golden_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/category_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/category_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/category_red_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/dialog_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/dialog_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/ic_confirm_btn_golden_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/ic_confirm_btn_golden_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/ic_confirm_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/ic_confirm_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/ic_confirm_btn_red_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/ic_confirm_btn_red_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_back_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_back_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_bt_muisic_album_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_0.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_10.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_11.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_12.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_13.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_14.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_15.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_16.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_17.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_18.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_19.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_3.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_4.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_5.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_6.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_7.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_8.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_loading_img_9.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_more_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_more_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_next_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_next_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_pause_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_pause_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_picture_play_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_picture_play_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_play_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_play_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_prev_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_prev_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_video_open_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/media_video_open_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/msg_type_a_btn_confirm_bg_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/msg_type_a_btn_confirm_bg_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/msg_type_d_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_01.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_02.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_03.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_04.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_05.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_06.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_07.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_08.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_09.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_10.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_11.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_12.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_13.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_14.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_15.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_16.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_17.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_18.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_19.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_20.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_21.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_22.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_23.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_24.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_25.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_26.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_27.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_28.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_29.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_30.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_31.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_32.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_33.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_34.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_35.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_36.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_37.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_38.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_39.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_40.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_41.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_42.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_43.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_44.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_45.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_46.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_47.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_48.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_49.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_50.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_51.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_52.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_53.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_54.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_55.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_56.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_57.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_58.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_59.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_60.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_61.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_62.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_63.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_64.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_65.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_66.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_67.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_68.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_69.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_70.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_71.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_72.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_73.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_74.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_75.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_76.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_77.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_78.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_79.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_80.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_81.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_82.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_83.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_84.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_85.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/player_bg_anim_86.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/playlist_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/playlist_golden_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/playlist_red_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/playlist_scroll.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_bt_connection_icon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_bt_delete_icon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_bt_device.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_bt_scanning.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_common_arrow_right_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_common_arrow_right_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_common_btn_normal.9.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_common_btn_pressed.9.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_common_btn_switch_off.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_common_btn_switch_on.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_common_default_cancel_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_common_default_cancel_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_common_default_confirm_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_common_default_confirm_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_common_default_dialog_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_common_gold_btn_switch_on.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_common_red_btn_switch_on.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_dialog_title_arrow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_divider_line.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_icon_loading_large.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_msg_choice_multiple_checked.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_msg_choice_multiple_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_refresh_wifi_list_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_refresh_wifi_list_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/setting_vehicle_list_line.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/switch_off.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/switch_on.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/tab_next_disable.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/tab_next_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/tab_next_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/tab_pre_disable.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/tab_pre_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-hdpi/tab_pre_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-xhdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-xhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-xxhdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-xxhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-xxxhdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/values-zh/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/values/attrs.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/values/colors.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/values/dimens.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/values/styles.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/main/res/values/themes.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Bluetooth/src/test/java/com/yaxon/bluetooth/ExampleUnitTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/libs/litepal-1.3.1.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/androidTest/java/com/yaxon/bluetoothsdk/ExampleInstrumentedTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/YXApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/constant/Constant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/framework/debug/CrashExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/framework/debug/LogcatHelper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/framework/debug/MemoneryLocatHelper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/framework/sharedpref/SharedPrefsManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/framework/sharedpref/SharedPrefsTag.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/framework/sharedpref/SystemSettingsManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/framework/sharedpref/SystemSharedTag.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/han2pinyin/Pinyin.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/han2pinyin/PinyinCode1.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/han2pinyin/PinyinCode2.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/han2pinyin/PinyinCode3.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/han2pinyin/PinyinData.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/interfaceclass/IBluetoothControl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/interfaceclass/IModuleBase.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/interfaceclass/music/IBluetoothMusic.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/interfaceclass/music/IBluetoothMusicPresent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/interfaceclass/phone/IBluetoothPhone.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/interfaceclass/phone/IBluetoothPhonePresent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/interfaceclass/setting/IBluetoothSetting.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/interfaceclass/setting/IBluetoothSettingPresent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/manager/BtControlManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/manager/BtGocBaseManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/manager/BtModuleBaseManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/manager/BtMusicManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/manager/BtOriginalBaseManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/manager/BtPhoneManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/manager/BtSettingManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/manager/InteractionManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/manager/RingManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/model/BluetoothNameBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/model/CallRecord.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/model/CallRecordBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/model/LinkedInfo.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/model/LinkedInfoBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/model/MultiPhoneBook.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/model/MusicInfoBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/model/PhoneBook.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/model/PhoneBookBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/music/MediaBrowserHelper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/AsyncQueryLiveData.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/CallHistoryLiveData.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/ContactSortingInfo.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/FutureData.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/HeartBeatLiveData.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/InMemoryPhoneBook.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/LiveDataFunctions.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/ObservableAsyncQuery.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/QueryParam.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/SharedPreferencesLiveData.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/SortedContactListLiveData.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/UiCallLog.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/UiCallLogLiveData.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/model/Contact.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/model/I18nPhoneNumberWrapper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/model/PhoneCallLog.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/model/PhoneNumber.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/pbab/model/PostalAddress.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/platform/BluetoothA2dpClient.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/platform/BluetoothAvrcpClient.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/platform/BluetoothConnectListenerClient.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/platform/BluetoothDeviceClient.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/platform/BluetoothHfpClient.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/platform/BluetoothMapConnectClient.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/platform/BluetoothPbapClient.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/utils/Chinese2PinyinUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/utils/ConnectingUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/utils/DateUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/utils/FastClickUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/utils/InputFilterUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/utils/IntentUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/utils/KeyBoardUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/utils/NullUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/utils/ParseUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/utils/ProgramLoaderUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/utils/TelecomUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/utils/UIUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/voice/BluetoothVoiceCmd.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/voice/BtVoiceManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/voice/IManagerLifeCycle.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/voice/IVoiceCmdCallback.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/java/com/yaxon/bluetoothsdk/voice/IVoiceControl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/res/raw/ring.mp3" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/res/values/arrays.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_BluetoothSDK/src/test/java/com/yaxon/bluetoothsdk/ExampleUnitTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/Android.mk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/proguard-rules.pro" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/java/com/yaxon/camera/BackcarActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/java/com/yaxon/camera/BackcarProvider.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/java/com/yaxon/camera/CameraHolder.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/java/com/yaxon/camera/CameraSurfaceView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/java/com/yaxon/camera/MyApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/java/com/yaxon/camera/debug/CrashExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/res/drawable-hdpi/backcar_nocamera_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/res/drawable-hdpi/icon_360_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/res/drawable-hdpi/setting_common_return_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/res/drawable-hdpi/setting_common_return_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/res/drawable/selector_common_back.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/res/layout/backcar_layout.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/res/values-zh-rCN/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/res/values/colors.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/res/values/dimens.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Camera/src/main/res/values/styles.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/proguard-rules.pro" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/androidTest/java/com/yaxon/factory/ExampleInstrumentedTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/MyApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/constant/Constant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/crash/CrashExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/data/BrightnessInitData.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/data/EqInitData.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/data/VolumeInitData.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/data/WallpaperInitData.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/dialog/YXToast.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/manager/AppInstallManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/manager/AudioSourceManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/manager/CommonManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/manager/FileManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/manager/GpsTimeUpdate.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/manager/ItsManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/manager/NetTimeUpdate.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/manager/PackagePermissionManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/manager/SpeechResourceManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/manager/SyncTimeManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/manager/SystemSettingsManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/manager/TimerManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/manager/VehicleStateManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/receiver/BootReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/service/CommonService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/util/BrightnessUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/util/BroadcastUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/util/DataUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/util/IntentUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/util/StateUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/util/TimeUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/util/VolumeUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/drawable-v24/ic_launcher_foreground.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/drawable/ic_launcher_background.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/layout/common_dialog.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/mipmap-anydpi-v26/ic_launcher.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/mipmap-hdpi/common_default_dialog_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/mipmap-hdpi/common_icon_loading_large.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/mipmap-hdpi/factory_icon_wallpaper_default.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/mipmap-hdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/mipmap-hdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/mipmap-mdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/mipmap-mdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/mipmap-xhdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/mipmap-xhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/mipmap-xxhdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/mipmap-xxhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/mipmap-xxxhdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/values-night/themes.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/values-zh/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/values/arrays.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/values/colors.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/values/dimens.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/values/themes.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/main/res/xml/network_security_config.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Factory/src/test/java/com/yaxon/factory/ExampleUnitTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/libs/commons-net-3.8.0.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/Constant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/OTAApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/activity/MainActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/connect/NetChangeObserver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/connect/OTAConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/connect/SocketManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/crash/CrashExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/ftp/DownLoadCallBack.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/ftp/FTPConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/ftp/FTPDownloadHelper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/ftp/FTPManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/ftp/FTPStatusCallBack.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/exceptions/SocketManagerException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/interfaces/On808DataCheckout.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/interfaces/OnGetResetDeviceList.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/interfaces/OnResetDeviceList.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/interfaces/SocketActionListener.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/interfaces/SocketPulseListener.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/interfaces/StartLocationBackList.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/jt808bean/Generate808andSeqBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/jt808bean/JT808Bean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/jt808bean/JT808HeaderBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/jt808bean/JT808RegisterBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/jt808bean/JT808ReplyBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/jt808bean/JT808UpdateBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/jt808bean/Jt808MapLocation.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/jt808coding/JT808Coding.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/jt808coding/JT808Directive.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/jt808coding/Jt808StickDataUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/jt808utils/BCD8421Operater.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/jt808utils/BitOperator.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/jt808utils/ByteUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/jt808utils/HexUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/jt808utils/HexUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/jt808utils/TimeUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/protocol/JT808Constant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/protocol/JT808ReaderProtocol.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/socketbean/PulseData.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/jt808/socketbean/SendDataBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/impl/client/AbsConnectionManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/impl/client/ConnectionManagerImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/impl/client/ManagerHolder.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/impl/client/PulseManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/impl/client/abilities/IConnectionSwitchListener.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/impl/client/action/ActionDispatcher.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/impl/client/action/ActionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/impl/client/iothreads/DuplexReadThread.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/impl/client/iothreads/DuplexWriteThread.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/impl/client/iothreads/IOThreadManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/impl/client/iothreads/SimplexIOThread.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/impl/exceptions/DogDeadException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/impl/exceptions/ManuallyDisconnectException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/impl/exceptions/UnConnectException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/sdk/OkSocket.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/sdk/client/ConnectionInfo.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/sdk/client/OkSocketFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/sdk/client/OkSocketOptions.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/sdk/client/OkSocketSSLConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/sdk/client/action/IAction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/sdk/client/action/ISocketActionListener.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/sdk/client/action/SocketActionAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/sdk/client/bean/IPulse.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/sdk/client/connection/AbsReconnectionManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/sdk/client/connection/DefaultReconnectManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/sdk/client/connection/IConnectionManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/sdk/client/connection/NoneReconnect.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/sdk/client/connection/abilities/IConfiguration.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/client/sdk/client/connection/abilities/IConnectable.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/core/exceptions/ReadException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/core/exceptions/WriteException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/core/iocore/AbsReader.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/core/iocore/ReaderImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/core/iocore/WriterImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/core/iocore/interfaces/IIOCoreOptions.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/core/iocore/interfaces/IOAction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/core/iocore/interfaces/IPulseSendable.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/core/iocore/interfaces/IReader.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/core/iocore/interfaces/ISendable.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/core/iocore/interfaces/IStateSender.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/core/iocore/interfaces/IWriter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/core/pojo/OriginalData.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/core/protocol/IReaderProtocol.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/core/utils/BytesUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/core/utils/LogUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/interfaces/basic/AbsLoopThread.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/interfaces/common_interfacies/IIOManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/interfaces/common_interfacies/client/IDisConnectable.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/interfaces/common_interfacies/client/ISender.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/interfaces/common_interfacies/dispatcher/IRegister.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/interfaces/common_interfacies/server/IClient.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/interfaces/common_interfacies/server/IClientIOCallback.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/interfaces/common_interfacies/server/IClientPool.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/interfaces/common_interfacies/server/IServerActionListener.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/interfaces/common_interfacies/server/IServerManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/interfaces/common_interfacies/server/IServerManagerPrivate.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/interfaces/common_interfacies/server/IServerShutdown.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/interfaces/default_protocol/DefaultNormalReaderProtocol.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/interfaces/default_protocol/DefaultX509ProtocolTrustManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/interfaces/utils/SPIUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/interfaces/utils/TextUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/oksocket/interfaces/utils/ThreadUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/option/CustomerNumber.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/option/PhoneNumber.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/option/TerminalModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/receiver/OTABroadcastReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/service/SocketService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/sharedpref/SharedPrefsManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/sharedpref/SharedPrefsTag.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/update/IUpdateFile.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/update/PreconditionTool.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/update/UpdateContract.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/update/UpdateParser.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/update/UpdateState.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/utils/DeviceUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/utils/FileUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/utils/MD5Util.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/utils/NetworkUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/utils/ReflectManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/utils/SnUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/java/com/yaxon/fota/utils/VersionUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/drawable-v24/ic_launcher_foreground.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/drawable/ic_launcher_background.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/drawable/layer_progress.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/drawable/select_button.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/drawable/shape_button.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/drawable/shape_button_p.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/layout/activity_main.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/mipmap-anydpi-v26/ic_launcher.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/mipmap-hdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/mipmap-hdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/mipmap-mdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/mipmap-mdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/mipmap-mdpi/main_background.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/mipmap-xhdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/mipmap-xhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/mipmap-xxhdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/mipmap-xxhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/mipmap-xxxhdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/values-night/themes.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/values/colors.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/values/dimens.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Fota/src/main/res/values/themes.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_GpioService/Android.mk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_GpioService/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_GpioService/libs/android.hardware.automotive.cargpio-V1.0-java.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_GpioService/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_GpioService/src/main/aidl/com/yaxon/gpio_service/IGpioListener.aidl" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_GpioService/src/main/aidl/com/yaxon/gpio_service/IGpioServiceManager.aidl" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_GpioService/src/main/java/com/yaxon/gpio_service/GpioCallback.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_GpioService/src/main/java/com/yaxon/gpio_service/GpioService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_GpioService/src/main/java/com/yaxon/gpio_service/GpioServiceManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_GpioService/src/main/java/com/yaxon/gpio_service/WeakHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/proguard-rules.pro" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/androidTest/java/com/yaxon/hvac/ExampleInstrumentedTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/java/com/yaxon/hvac/HvacApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/java/com/yaxon/hvac/activity/HvAcActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/drawable/ac_defrosting_anim.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/drawable/seekbar_style.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/drawable/selector_ac_cold.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/drawable/selector_ac_cold_golden.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/drawable/selector_ac_cold_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/drawable/selector_ac_loop.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/drawable/selector_ac_loop_golden.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/drawable/selector_ac_loop_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/drawable/selector_ac_on.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/drawable/selector_ac_on_golden.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/drawable/selector_ac_on_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/drawable/selector_air_mode.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/drawable/selector_fan_speed2.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/drawable/selector_predefrost.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/drawable/selector_predefrost_golden.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/drawable/selector_predefrost_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/layout/activity_main_ac.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_ac_golden_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_ac_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_ac_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_ac_red_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_defroster.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_feet.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_feetanddefroster.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_golden_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_hand.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_handandfeet.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_iconfan_big.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_iconfan_small.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_innerloop_golden_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_innerloop_golden_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_innerloop_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_innerloop_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_innerloop_red_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_innerloop_red_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_maxac_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_maxac_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_on_golden_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_on_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_on_r_ic5.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_on_red_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_postdefrost_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_postdefrost_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_predefrost_golden_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_predefrost_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_predefrost_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_predefrost_red_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_red_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_snow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_sun.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_wind_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/airconditioner_windmode_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting100.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting102.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting104.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting106.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting108.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting110.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting112.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting114.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting116.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting118.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting120.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting122.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting124.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting126.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting128.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting130.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting132.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting20.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting22.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting24.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting26.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting28.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting30.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting32.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting34.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting36.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting38.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting40.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting42.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting44.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting46.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting48.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting50.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting52.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting54.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting56.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting58.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting60.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting62.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting64.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting66.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting68.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting70.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting72.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting74.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting76.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting78.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting80.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting82.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting84.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting86.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting88.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting90.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting92.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting94.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting96.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/defrosting98.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/droparrow_up.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/electric_wind0.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/electric_wind1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/electric_wind2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/electric_wind3.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/electric_wind4.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/electric_wind5.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/electric_wind6.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/electric_wind7.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/n748_off_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/n748_on_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-hdpi/n748_on_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-mdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/mipmap-mdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/values/attrs.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/values/colors.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/values/dimens.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/main/res/values/themes.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Hvac/src/test/java/com/yaxon/hvac/ExampleUnitTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/proguard-rules.pro" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testBt_call.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testBt_close.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testBt_getAddress.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testBt_music.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testBt_open.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testMusic_close.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testMusic_getPath.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testMusic_play.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testRadioFM_close.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testRadioFM_freq.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testRadioFM_open.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testRadioFM_pre.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testTTS_close.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testTTS_start.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testVideo_close.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testVideo_play.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testWifi_close.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testWifi_connect.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testWifi_getWifiList.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/sh/testWifi_open.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/activity/MainActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/activity/TestBaseActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/activity/TestLocationActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/activity/TestManualActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/activity/TestScreenActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/activity/TestWifiActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/adapter/ManualTestListViewAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/base/CrashExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/base/YXApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/burn_in/AppTimerTaskManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/burn_in/BurnInState.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/burn_in/FloatingWindowManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/burn_in/LogcatHelper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/controller/BaseProtocolController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/controller/ConstDef_ITS.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/controller/ConstDef_M0.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/controller/ControllerFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/controller/McuCanController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/controller/McuSerialPortController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/controller/MonitorTransferData.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/controller/ProtocolDataHelper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/controller/business/BaseBusiness.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/controller/business/DefaultBusiness.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/data/CanDataFrame.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/data/CanProtocolFrame.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/data/ProtocolFrame.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/data/SensorStatus.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/data/TestAckBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/data/TestAppConstant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/data/TestAppInfo.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/data/TestConstant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/data/TestItemInfo.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/manager/GpsManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/receiver/AckReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/receiver/BootReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/service/BurnInTestService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/service/ItsService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/utils/CPUUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/utils/CommandType.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/utils/DataConvert.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/utils/ItsAppUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/utils/JsonUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/utils/KeyType.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/utils/NumberParseUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/utils/SimCardUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/utils/StorageUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/utils/VolumeUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/views/AbsBaseTestLayout.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/views/AuxView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/views/BackLightView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/views/BlankView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/views/CameraSurfaceView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/views/CameraView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/views/CanLineView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/views/FloatingNaviView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/views/HintDialog.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/views/ILayout.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/views/MicRecordView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/views/TouchKeyView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/views/TouchScreenView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/views/WiFiDialog.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/java/com/yaxon/its/views/WiFiHotspotView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/btn_adjust_next_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/btn_adjust_next_selected.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/btn_adjust_pre_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/btn_adjust_pre_selected.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/btn_auto_next_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/btn_auto_next_selected.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/btn_auto_pre_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/btn_auto_pre_selected.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/btn_pause.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/btn_pause_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/btn_play.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/btn_play_pressed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/check_layout_bg.9.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/seekbar_thumb.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/testrgb1.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/testrgb2.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable-mdpi/testrgb3.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable/btn_adjust_next.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable/btn_adjust_pre.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable/btn_auto_next.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable/btn_auto_pre.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable/btn_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable/btn_pause_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable/btn_play_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable/down_progressbar.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable/ic_cancel_btn_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable/ic_confirm_btn_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable/ic_launcher_background.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable/selector_common_back.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable/selector_setting_checkbox.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable/shap_round_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable/shap_round_its.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable/share_common_line.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/drawable/share_seekbar.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/activity_main.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/activity_test_location.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/activity_test_manual.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/activity_test_screen.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/activity_test_wifi.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/layout_common_dialog.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/layout_floating_navi_view.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/layout_floating_view.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/layout_manual_test_listview_item.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/layout_radio_group.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/view_canline.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/view_test_back_light.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/view_test_camera.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/view_test_micrecording.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/view_touch_key.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/view_wifi_hotspot.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/wifi_dialog_layout.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/layout/wifi_item_list_layout.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/mipmap-hdpi/common_dialog_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/mipmap-hdpi/ic_cancel_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/mipmap-hdpi/ic_cancel_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/mipmap-hdpi/ic_confirm_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/mipmap-hdpi/ic_confirm_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/mipmap-hdpi/setting_common_return_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/mipmap-hdpi/setting_common_return_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/mipmap-mdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/mipmap-mdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/mipmap-mdpi/ic_seekbar_hand.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/mipmap-mdpi/icon_checkbox_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/mipmap-mdpi/icon_checkbox_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/values/colors.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/values/dimens.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_ITS/src/main/res/values/styles.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/Android" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/aidl/com/yaxon/radio/IRadioPlayCtrl.aidl" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/RadioApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/adapter/FreqListAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/adapter/HorizonFreqListAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/base/BaseBindingActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/base/BaseBindingFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/base/BaseBindingModelActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/base/BaseMvvmModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/constant/ConstantsDef.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/receiver/BootReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/service/RadioPlayCtrolStub.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/service/RadioService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/utils/FileManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/utils/IntentUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/utils/SystemSettingUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/view/FavFragment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/view/FreqListActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/view/RadioMainActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/viewmodel/RadioContract.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/viewmodel/RadioListViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/viewmodel/RadioViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/widget/DeleteDialog.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/widget/GalleryLayoutManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/widget/ReflectTextView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/widget/RefreshingDialog.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/widget/RulerView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/widget/SlideRecyclerView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/widget/SpeedRecyclerView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/widget/TopSmoothScroller.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/java/com/yaxon/radio/widget/Vp2IndicatorView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/anim/loading_rotate_anim.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/color/radio_tab_blue_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/color/radio_tab_golden_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/color/radio_tab_red_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable-v24/ic_launcher_foreground.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/drawable_anim.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/fm_select_line_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_btn_delete_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_btn_eq_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_btn_list_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_btn_next_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_btn_pause_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_btn_play_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_btn_player_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_btn_prev_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_btn_update_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_cancel_btn_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_category_golden_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_category_red_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_category_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_confirm_btn_golden_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_confirm_btn_red_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_confirm_btn_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_fav_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_item_freq_blue_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_item_freq_golden_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_item_freq_red_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_launcher_background.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_loading_anim.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/drawable/ic_radio_search_selector.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/layout/activity_freq_list.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/layout/activity_main.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/layout/activity_radio_main.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/layout/fragment_fav.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/layout/horizo_freq_list_item.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/layout/item_freq_list.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/layout/layout_botttom_aircondition.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/layout/layout_common_dialog.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/layout/layout_refresh_dialog.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/ac_modes_n_r.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/airconditioner_iconfan_small.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/back_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/back_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/band_divider.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/bottom_bar_dislike.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/bottom_bar_fav.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/bottom_bar_pause_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/bottom_bar_pause_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/bottom_bar_play_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/bottom_bar_play_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/bottom_bar_progress_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/button_disabled.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/button_negative_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/button_negative_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/button_positive_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/button_positive_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/category_golden_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/category_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/category_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/category_red_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/common_dialog_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/cover_watermark.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/default_avatar.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/default_cover.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/delete_all_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/delete_all_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/dialog_bg.9.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/divider.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/everyday_recomand_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/home_background.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/home_feet_defrost_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/home_incycle_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/home_temperature_gear3.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/ic_cancel_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/ic_cancel_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/ic_confirm_btn_golden_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/ic_confirm_btn_golden_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/ic_confirm_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/ic_confirm_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/ic_confirm_btn_red_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/ic_confirm_btn_red_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/ic_loading.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/ic_radio_search_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/ic_radio_search_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/ic_radio_stereo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/login_state_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/login_state_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/main_bottom_bar_progress.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/main_bottom_bar_progress_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/main_more_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/main_more_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/main_search_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/main_search_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/media_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/media_collect_list_img.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/media_delete_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/media_delete_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/media_radio_ruler_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/media_search_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/media_search_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/media_update_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/media_update_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/mid_cover_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/more_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/more_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/music_playing_1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/music_playing_10.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/music_playing_11.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/music_playing_12.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/music_playing_13.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/music_playing_14.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/music_playing_15.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/music_playing_16.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/music_playing_17.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/music_playing_2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/music_playing_3.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/music_playing_4.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/music_playing_5.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/music_playing_6.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/music_playing_7.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/music_playing_8.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/music_playing_9.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/my_favorite_more_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/my_favorite_more_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/my_fm_favorite_delete_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/my_history_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/my_history_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/off_modes_n_r.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/play_all_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/play_all_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/player_dislike_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/player_dislike_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/player_fav_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/player_fav_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/player_next_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/player_next_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/player_pause_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/player_pause_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/player_play_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/player_play_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/player_playlist_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/player_playlist_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/player_pre_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/player_pre_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/player_setting_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/player_setting_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/player_shadow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/playlist_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/playlist_clear_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/playlist_clear_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/playlist_dialog_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/playlist_golden_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/playlist_red_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/playlist_scroll.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/predefrost_n748_n_r.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/radio_divider_line.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/radio_transparent_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-hdpi/underline.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-mdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-mdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-xxhdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-xxhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-xxxhdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/values-zh/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/values/attrs.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/values/colors.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/values/dimens.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Radio/src/main/res/values/themes.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_RadioService/Android.mk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_RadioService/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_RadioService/libs/android.hardware.automotive.carradio-V1.0-java.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_RadioService/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_RadioService/src/main/aidl/com/yaxon/radio_service/IRadioListener.aidl" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_RadioService/src/main/aidl/com/yaxon/radio_service/IRadioServiceManager.aidl" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_RadioService/src/main/java/com/yaxon/radio_service/RadioCallback.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_RadioService/src/main/java/com/yaxon/radio_service/RadioService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_RadioService/src/main/java/com/yaxon/radio_service/RadioServiceManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_RadioService/src/main/java/com/yaxon/radio_service/WeakHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/Android" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/SystemUIApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/bluetooth/BluetoothConnectListenerClient.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/bluetooth/BluetoothHfpClient.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/bluetooth/MultiPhoneBookBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/bluetooth/PhoneBookBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/bluetooth/PhoneStateBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/brightness/BrightnessDialog.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/callback/IHvacNavigationCallback.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/callback/IPhoneStateCallback.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/callback/KeyCodeController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/callback/KeyCodeControllerImp.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/constant/Constant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/controller/BaseHvacNavigationController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/controller/HvacControllerFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/controller/HvacNavigationController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/controller/JacLEF20HvacController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/framework/sharedpref/SystemSettingsManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/manager/BtPhoneManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/manager/ToastManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/manager/VirtualManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/model/CanDataFrame.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/model/CanProtocolFrame.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/model/ConstantCanID.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/model/DataStore.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/model/HvacNavigationConstant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/model/ProtocolFrame.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/model/TireMonitorData.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/mvvm/HvacNavigationContract.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/mvvm/HvacNavigationModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/mvvm/HvacNavigationViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/mvvm/PhoneStateContract.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/mvvm/PhoneStateModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/mvvm/PhoneViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/utils/BrightnessUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/utils/BroadcastUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/utils/CrashExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/utils/DataConvert.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/utils/ProgramLoaderUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/utils/SystemUiUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/utils/VolumeUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/VolumeDialogView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/VolumeSeekBar.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/qs/BtTitle.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/qs/DefaultTile.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/qs/PanoramaTitle.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/qs/QsTileViewHelper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/qs/QsViewContainer.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/qs/RebotView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/qs/StandbyTitle.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/qs/WifiApTile.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/qs/WifiTile.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/state/BtView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/state/LabelView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/state/NavigationView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/state/PanelTextClock.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/state/PhoneStateSmallView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/state/PhoneStateView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/state/SIMView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/state/StatuBarPanelView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/state/StatusBarView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/state/TimeTextClock.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/state/UsbView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/state/VolumeView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/state/WifiApView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/view/state/WifiView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/voice/IManagerLifeCycle.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/voice/VoiceCmdManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/java/com/android/systemui/sdk/voice/VoiceCtrlManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/bluetooth_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/bluetooth_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/bluetooth_p_gold.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/bluetooth_p_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/bt_phone_bar_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/panorama_360_p_gold.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/panorama_360_p_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/panorama_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/panorama_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/standby_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/standby_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/standby_p_gold.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/standby_p_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/test_qs_wifi_connected.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/test_qs_wifi_disconnect.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/test_qs_wifiap_off.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/test_qs_wifiap_on.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/wifi_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/wifi_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/wifi_p_gold.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable-mdpi/wifi_p_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/bt_sharp_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/iv_hangup.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/iv_pickup.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/seekbar_blue_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/seekbar_gold_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/seekbar_red_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/selector_acmodes_blue.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/selector_acmodes_gold.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/selector_acmodes_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/selector_droparrow.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/selector_home_feet_level_blue.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/selector_home_feet_level_gold.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/selector_home_feet_level_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/selector_homecycle_blue.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/selector_homecycle_gold.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/selector_homecycle_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/selector_modes_blue.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/selector_modes_gold.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/selector_modes_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/selector_predefrost_blue.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/selector_predefrost_gold.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/selector_predefrost_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/selector_temperature_level.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/test_selector_qs_bt_blue.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/test_selector_qs_bt_gold.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/test_selector_qs_bt_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/test_selector_qs_panorama.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/test_selector_qs_panorama_gold.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/test_selector_qs_panorama_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/test_selector_qs_standby.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/test_selector_qs_standby_gold.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/test_selector_qs_standby_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/test_selector_qs_wifi.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/test_selector_qs_wifi_gold.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/test_selector_qs_wifi_red.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/drawable/test_selector_qs_wifiap.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/layout/brightness_dialog.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/layout/mascot_layout.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/layout/navi_bar_view.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/layout/navi_bar_view_layout.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/layout/phone_bar_small_layout.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/layout/phone_bar_small_view.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/layout/phone_bar_view.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/layout/phone_bar_view_layout.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/layout/status_bar_panel_view.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/layout/status_bar_panel_view_layout.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/layout/status_bar_view.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/layout/status_bar_view_layout.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/layout/systemui_toast_view.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/layout/volume_dialog_view.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/ac_modes_n_r.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/ac_modes_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/ac_modes_p_gold.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/ac_modes_p_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/airconditioner_iconfan_small.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/bg_shutdown.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/bt_call_button_hangup_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/bt_call_button_hangup_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/bt_call_button_pickup_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/bt_call_button_pickup_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/bt_call_button_transform_phone_n_yellow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/bt_call_button_transform_phone_p_blue.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/bt_call_button_transform_phone_p_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/bt_call_button_transform_phone_yellow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/bt_call_mini_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/bt_off.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/bt_phone_dial_mini_default_img.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/drop_guide.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/drop_media.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/drop_phone.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/drop_voice.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/droparrow_down.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_defrost_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_defrost_p_gold.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_defrost_p_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_feet_defrost_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_feet_defrost_p_gold.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_feet_defrost_p_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_feet_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_feet_p_gold.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_feet_p_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_hand_feet_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_hand_feet_p_gold.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_hand_feet_p_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_hand_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_hand_p_gold.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_hand_p_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_incycle_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_incycle_p_gold.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_incycle_p_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_outcycle_n_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_outcycle_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_outcycle_p_gold.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_temperature_gear.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_temperature_gear_eight.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_temperature_gear_eleven.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_temperature_gear_five.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_temperature_gear_fiveteen.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_temperature_gear_four.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_temperature_gear_fourteen.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_temperature_gear_nine.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_temperature_gear_one.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_temperature_gear_seven.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_temperature_gear_six.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_temperature_gear_ten.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_temperature_gear_thirteen.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_temperature_gear_three.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_temperature_gear_twelve.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_temperature_gear_zero.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/home_temperature_geartwo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/icon_bottom_devider_b.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/icon_bottom_devider_g.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/icon_bottom_devider_r.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/icon_mascot.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/mute_off.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/mute_on.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/off_modes_n_r.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/on_modes_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/on_modes_n_gold.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/on_modes_n_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/predefrost_n_n_r.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/predefrost_n_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/predefrost_n_p_gold.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/predefrost_n_p_red.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/setting_common_thumb_bar_icon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/setting_max_brightness.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/sim_four_g_five.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/sim_four_g_four.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/sim_four_g_one.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/sim_four_g_three.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/sim_four_g_two.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/sim_four_g_zero.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/statubar_divider_line.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/statusbar_close_mascot_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/systemui_common_default_dialog_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/usb_off.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/usb_on.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/virtual_volume_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/virtual_volume_small_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/volume_droparrow_up.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/volume_panel_bar_icon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/wifi_ap_on.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/wifi_on.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/wifi_signal_four.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/wifi_signal_one.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/wifi_signal_three.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/wifi_signal_two.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/mipmap-hdpi/wifi_signal_zero.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/values-zh/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/values/colors.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/values/dimens.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_SystemUI/src/main/res/values/style.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/androidTest/java/com/yaxon/yx_tiremonitor/ExampleInstrumentedTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/TireAppLication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/TireErrorAdapter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/activity/TireMonitoreActivity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/callback/ITireCallBack.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/callback/YXOnClickListener.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/constant/ConstDef_Can.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/constant/Constant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/controller/BaseHvacController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/controller/TireControllerFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/controller/YaXonController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/controller/impl/JavTireController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/model/CanDataFrame.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/model/CanProtocolFrame.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/model/ProtocolFrame.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/model/TireMonitorData.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/mvvm/TireContract.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/mvvm/TireModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/mvvm/TireViewModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/sharedpref/SystemSettingsManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/sharedpref/SystemSharedTag.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/utils/CrashExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/utils/DataConvert.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/utils/TireUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/utils/ToastUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/views/YXDialog.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/java/com/yaxon/yx_tiremonitor/views/YXToast.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/drawable-v24/ic_launcher_foreground.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/drawable/base_toast_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/drawable/ic_launcher_background.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/drawable/selector_common_back.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/drawable/selector_common_default_cancel_btn.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/drawable/selector_common_default_confirm_btn.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/drawable/selector_common_gold_confirm_btn.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/drawable/selector_common_progressbar.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/drawable/selector_common_red_confirm_btn.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/drawable/selector_dialog_btn.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/drawable/selector_edittext_corner_bg.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/drawable/selector_msg_choice_multiple.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/drawable/selector_progressbar.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/drawable/selector_tire_left.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/drawable/selector_tire_left_down.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/drawable/selector_tire_right.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/drawable/selector_tire_right_down.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/layout/base_layout_toast.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/layout/item_tire_list.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/layout/tire_activity.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/layout/tire_common_dialog.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/layout/tire_common_toast.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-anydpi-v26/ic_launcher.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/car_tire_model.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/default_wallpaper_day.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/dialog_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/dialog_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/ic_launcher.webp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/ic_launcher_round.webp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/setting_common_default_cancel_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/setting_common_default_cancel_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/setting_common_default_confirm_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/setting_common_default_confirm_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/setting_common_default_dialog_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/setting_common_gold_confirm_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/setting_common_gold_confirm_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/setting_common_red_confirm_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/setting_common_red_confirm_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/setting_common_return_btn_n.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/setting_common_return_btn_p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/setting_dialog_title_arrow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/setting_icon_loading_large.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/setting_msg_choice_multiple_checked.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/setting_msg_choice_multiple_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/tire_left_down_low.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/tire_left_down_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/tire_left_low.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/tire_left_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/tire_right_down_low.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/tire_right_down_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/tire_right_low.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/tire_right_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/tp_status_abnormal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/tp_status_normal.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-hdpi/tp_status_warning.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-mdpi/ic_launcher.webp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-mdpi/ic_launcher_round.webp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-xhdpi/ic_launcher.webp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-xhdpi/ic_launcher_round.webp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-xxhdpi/ic_launcher.webp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-xxhdpi/ic_launcher_round.webp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-xxxhdpi/ic_launcher.webp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/mipmap-xxxhdpi/ic_launcher_round.webp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/values-night/themes.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/values-zh/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/values/arrays.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/values/colors.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/values/dimen.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/main/res/values/themes.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_TireMonitor/src/test/java/com/yaxon/yx_tiremonitor/ExampleUnitTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/libs/mvwservice.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/libs/serial-0.1.6_classes.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/libs/ttsservice.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/proguard-rules.pro" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/androidTest/java/com/yaxon/voice/ExampleInstrumentedTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/Constant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/VoiceApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/bean/Semantic.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/bean/VoiceBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/debug/CrashExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/gps/GpsBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/gps/Semantic.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/manager/AcVoiceManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/manager/AppStateManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/manager/AutoNaviManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/manager/CarCtrlVoiceManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/manager/GpsLocationManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/manager/MediaVoiceManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/manager/PhoneVoiceManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/manager/SourceManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/manager/SpeechSettingManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/manager/SystemVoiceManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/manager/TtsManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/manager/VoiceEventManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/manager/VoiceManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/manager/WakeUpManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/query/QueryBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/query/Semantic.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/receiver/BootReceiver.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/respond/Data.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/respond/Semantic.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/respond/VoiceSendBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/service/VoiceMessageManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/service/VoiceService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/set/Data.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/set/Semantic.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/set/SetBean.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/utils/AudioFocusUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/utils/BlueToothUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/utils/BrightnessUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/utils/BroadcastUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/utils/CommandType.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/utils/FileUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/utils/NumberDealUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/utils/SystemUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/utils/VolumeUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/utils/WifiUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/java/com/yaxon/voice/utils/YXMessage.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/res/mipmap-hdpi/ic_launcher.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/res/mipmap-hdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/res/values-night/themes.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/res/values/colors.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/res/values/strings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/main/res/values/themes.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/YX_Voice/src/test/java/com/yaxon/voice/ExampleUnitTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/build.gradle" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sdklibs/services_classes.jar" beforeDir="false" afterPath="$PROJECT_DIR$/sdklibs/services_classes.jar" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sdklibs/yx_support_classes.jar" beforeDir="false" afterPath="$PROJECT_DIR$/sdklibs/yx_support_classes.jar" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/settings.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/settings.gradle" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/GoolePinyin/Android.mk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/GoolePinyin/GoolePinyin.apk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/MJ/Android.mk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/MJ/MJ.apk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/SpeechHMI/Android.mk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/SpeechHMI/SpeechHMI.apk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_BlueTooth/Android.mk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_BlueTooth/YX_BlueTooth.apk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_Factory/Android.mk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_Factory/YX_Factory.apk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_Fota/Android.mk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_Fota/YX_Fota.apk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_Hvac/Android.mk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_Hvac/YX_Hvac.apk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_ITS/Android.mk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_ITS/YX_ITS.apk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_Music/Android.mk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_Music/YX_Music.apk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_Radio/Android.mk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_Radio/YX_Radio.apk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_SystemUI/Android.mk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_SystemUI/YX_SystemUI.apk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_TireMonitor/Android.mk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_TireMonitor/YX_TireMonitor.apk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_Voice/Android.mk" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/third_apps/YX_Voice/YX_Voice.apk" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$/YX_Support">
          <activation />
        </task>
        <task path="$PROJECT_DIR$">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="jianghui" type="f1a62948:ProjectNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="jianghui" type="f1a62948:ProjectNode" />
                <item name="YX_Support" type="2d1252cf:ModuleNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="jianghui" type="f1a62948:ProjectNode" />
                <item name="YX_Support" type="2d1252cf:ModuleNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="jianghui" type="f1a62948:ProjectNode" />
                <item name="YX_Support" type="2d1252cf:ModuleNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
                <item name="other" type="c8890929:TasksNode$1" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="layoutResourceFile_vertical" />
        <option value="resourceFile" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="GenerateSignedApkSettings">
    <option name="BUILD_TARGET_KEY" value="apk" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;tk666ttkk&quot;
  }
}</component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/tk666ttkk/test.git&quot;,
    &quot;accountId&quot;: &quot;92025a08-2515-47a4-a1fa-6316aacf159e&quot;
  }
}</component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$USER_HOME$/AppData/Local/Google/AndroidStudio2024.3/device-explorer/rockchip rk3568_r/_/sdcard/yaxon/crash/photo/1.crash-2025-06-04-21-15-57-*************.log" root0="SKIP_INSPECTION" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="202ABIlUSp49MKkefXYBEBIrl3d" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Android App.Launcher.executor&quot;: &quot;Run&quot;,
    &quot;Android App.MainActivity.executor&quot;: &quot;Run&quot;,
    &quot;Android App.Music.executor&quot;: &quot;Run&quot;,
    &quot;Android App.YX_DataCenterService.executor&quot;: &quot;Run&quot;,
    &quot;Android App.YX_Hvac.executor&quot;: &quot;Run&quot;,
    &quot;Android App.YX_ITS.executor&quot;: &quot;Run&quot;,
    &quot;Android App.YX_Radio.executor&quot;: &quot;Run&quot;,
    &quot;Android App.YX_Setting.executor&quot;: &quot;Run&quot;,
    &quot;Android App.YX_SystemUI.executor&quot;: &quot;Run&quot;,
    &quot;ApkExportedModule&quot;: &quot;jianghui.Launcher&quot;,
    &quot;DEBUGGABLE_DEVICE&quot;: &quot;rockchip-rk3568_r-C002D00000000000000000000200097?&quot;,
    &quot;DEBUGGABLE_PROCESS&quot;: &quot;com.yaxon.its&quot;,
    &quot;DEBUGGER_ID&quot;: &quot;Java&quot;,
    &quot;Gradle.jianghui [:Launcher:assembleRelease].executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;SHOW_ALL_PROCESSES&quot;: &quot;false&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;com.google.services.firebase.aqiPopupShown&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;develop&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/ProjetForAndroid/jianghui/jianghui&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.17&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.27342746&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;reference.settings.ide.settings.notifications&quot;
  }
}</component>
  <component name="PsdUISettings">
    <option name="MODULE_TAB" value="Default Config" />
    <option name="BUILD_VARIANTS_TAB" value="Build Types" />
    <option name="LAST_EDITED_SIGNING_CONFIG" value="debug" />
    <option name="LAST_EDITED_BUILD_TYPE" value="release" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\yaxon\workspace\jianghui\third_apps\YX_Setting" />
      <recent name="E:\yaxon\workspace\jianghui\third_apps\YX_Music" />
      <recent name="E:\yaxon\workspace\jianghui\Music\src\main\java\com\example\musicplay\view\videolist" />
      <recent name="E:\yaxon\workspace\jianghui\third_apps\YX_Voice" />
      <recent name="E:\yaxon\workspace\jianghui\third_apps\YX_ITS" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\ProjetForAndroid\jianghui\jianghui\feature-weather" />
      <recent name="E:\yaxon\workspace\jianghui\YX_DataCenterService\libs" />
      <recent name="E:\yaxon\workspace\jianghui\YX_Radio\src\main\res\mipmap-hdpi" />
      <recent name="E:\yaxon\workspace\jianghui\Radio\src\main\res\drawable" />
      <recent name="E:\yaxon\workspace\jianghui\Hvac\src\main\res\mipmap-hdpi" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.yaxon.radio" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.yaxon.datacenter_service.receiver" />
      <recent name="com.yaxon.launcher.utils" />
      <recent name="com.android.systemui.sdk.controller" />
      <recent name="com.yaxon.hvacsdk.controller.imp" />
      <recent name="com.yaxon.mcu_service.utils" />
    </key>
  </component>
  <component name="RunManager" selected="Android App.Launcher">
    <configuration default="true" type="AndroidJUnit" factoryName="Android JUnit">
      <option name="TEST_OBJECT" value="class" />
      <option name="WORKING_DIRECTORY" value="$MODULE_DIR$" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="BlueTooth" type="AndroidRunConfigurationType" factoryName="Android App">
      <module name="jianghui.BlueTooth" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Sample Java Methods" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Hvac" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="jianghui.Hvac" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Sample Java Methods" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Launcher" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="jianghui.Launcher.main" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="MainActivity" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false" temporary="true">
      <module name="jianghui.Launcher.main" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="specific_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="com.yaxon.launcher.main.MainActivity" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Music" type="AndroidRunConfigurationType" factoryName="Android App">
      <module name="jianghui.Music" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Photo" type="AndroidRunConfigurationType" factoryName="Android App">
      <module name="jianghui.Photo" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Sample Java Methods" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Radio" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="jianghui.Radio" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Sample Java Methods" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Setting" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="jianghui.Setting" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Sample Java Methods" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Video" type="AndroidRunConfigurationType" factoryName="Android App">
      <module name="jianghui.Video" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Sample Java Methods" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="YX_BlueTooth" type="AndroidRunConfigurationType" factoryName="Android App">
      <module name="jianghui.YX_BlueTooth" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="YX_Camera" type="AndroidRunConfigurationType" factoryName="Android App">
      <module name="jianghui.YX_Camera" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="YX_DataCenterService" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="jianghui.YX_DataCenterService.main" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="YX_EqService" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="jianghui.YX_EqService.main" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="YX_Factory" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="jianghui.YX_Factory" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="do_nothing" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="YX_Fota" type="AndroidRunConfigurationType" factoryName="Android App">
      <module name="jianghui.YX_Fota" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="YX_Hvac" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="jianghui.YX_Hvac" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="YX_ITS" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="jianghui.YX_ITS" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="YX_McuService" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="jianghui.YX_McuService.main" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="YX_MiscService" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="jianghui.YX_MiscService.main" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="YX_Radio" type="AndroidRunConfigurationType" factoryName="Android App">
      <module name="jianghui.YX_Radio" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="YX_RadioService" type="AndroidRunConfigurationType" factoryName="Android App">
      <module name="jianghui.YX_RadioService" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="do_nothing" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="YX_Setting" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="jianghui.YX_Setting.main" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="YX_SystemUI" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="jianghui.YX_SystemUI" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="do_nothing" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="YX_TireMonitor" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="jianghui.YX_TireMonitor" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="YX_Voice" type="AndroidRunConfigurationType" factoryName="Android App">
      <module name="jianghui.YX_Voice" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="do_nothing" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="jianghui [:Launcher:assembleRelease]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" value="" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value=":Launcher:assembleRelease" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="jianghui:YX_Support [makeJar]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$/YX_Support" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="makeJar" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Android App.BlueTooth" />
      <item itemvalue="Android App.Hvac" />
      <item itemvalue="Android App.Launcher" />
      <item itemvalue="Android App.Music" />
      <item itemvalue="Android App.Photo" />
      <item itemvalue="Android App.Radio" />
      <item itemvalue="Android App.Setting" />
      <item itemvalue="Android App.Video" />
      <item itemvalue="Android App.YX_BlueTooth" />
      <item itemvalue="Android App.YX_Camera" />
      <item itemvalue="Android App.YX_DataCenterService" />
      <item itemvalue="Android App.YX_EqService" />
      <item itemvalue="Android App.YX_Factory" />
      <item itemvalue="Android App.YX_Fota" />
      <item itemvalue="Android App.YX_Hvac" />
      <item itemvalue="Android App.YX_ITS" />
      <item itemvalue="Android App.YX_McuService" />
      <item itemvalue="Android App.YX_MiscService" />
      <item itemvalue="Android App.YX_RadioService" />
      <item itemvalue="Android App.YX_Radio" />
      <item itemvalue="Android App.YX_Setting" />
      <item itemvalue="Android App.YX_SystemUI" />
      <item itemvalue="Android App.YX_TireMonitor" />
      <item itemvalue="Android App.YX_Voice" />
      <item itemvalue="Android App.MainActivity" />
      <item itemvalue="Gradle.jianghui [:Launcher:assembleRelease]" />
      <item itemvalue="Gradle.jianghui:YX_Support [makeJar]" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Gradle.jianghui [:Launcher:assembleRelease]" />
        <item itemvalue="Android App.MainActivity" />
        <item itemvalue="Gradle.jianghui:YX_Support [makeJar]" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c52f9512-1994-499f-a2d1-081635403813" name="Default Changelist" comment="" />
      <created>1635232171567</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1635232171567</updated>
    </task>
    <servers />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="develop" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/Launcher/src/main/java/com/yaxon/launcher/view/widget/WidgetsMusic.java</url>
          <line>190</line>
          <option name="timeStamp" value="12" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/YX_Factory/src/main/java/com/yaxon/factory/manager/ItsManager.java</url>
          <line>40</line>
          <option name="timeStamp" value="16" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo />
      </pinned-members>
    </pin-to-top-manager>
  </component>
  <component name="play_dynamic_filters_status">
    <option name="appIdToCheckInfo">
      <map>
        <entry key="com.android.systemui">
          <value>
            <CheckInfo lastCheckTimestamp="1748520104080" />
          </value>
        </entry>
        <entry key="com.android.systemui.test">
          <value>
            <CheckInfo lastCheckTimestamp="1748520104080" />
          </value>
        </entry>
        <entry key="com.yaxon.bluetooth">
          <value>
            <CheckInfo lastCheckTimestamp="1750763320386" />
          </value>
        </entry>
        <entry key="com.yaxon.bluetooth.test">
          <value>
            <CheckInfo lastCheckTimestamp="1750763320384" />
          </value>
        </entry>
        <entry key="com.yaxon.factory.test" />
        <entry key="com.yaxon.launcher.test">
          <value>
            <CheckInfo lastCheckTimestamp="1752132271976" />
          </value>
        </entry>
        <entry key="com.yaxon.misc_service">
          <value>
            <CheckInfo lastCheckTimestamp="1748517910419" />
          </value>
        </entry>
        <entry key="com.yaxon.misc_service.test">
          <value>
            <CheckInfo lastCheckTimestamp="1748517910420" />
          </value>
        </entry>
        <entry key="com.yaxon.music">
          <value>
            <CheckInfo lastCheckTimestamp="1748524446546" />
          </value>
        </entry>
        <entry key="com.yaxon.music.test">
          <value>
            <CheckInfo lastCheckTimestamp="1748524446555" />
          </value>
        </entry>
        <entry key="com.yaxon.setting">
          <value>
            <CheckInfo lastCheckTimestamp="1750764718246" />
          </value>
        </entry>
        <entry key="com.yaxon.setting.test">
          <value>
            <CheckInfo lastCheckTimestamp="1750764718252" />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>