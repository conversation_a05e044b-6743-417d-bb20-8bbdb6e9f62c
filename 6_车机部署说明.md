# 车机Launcher部署和调试说明

## 问题解决方案

### 1. 关于本机界面空白问题

**问题描述**: 点击"关于本机"界面空白，无法启用开发者选项

**解决方案**:
```bash
# 运行修复脚本
.\fix_about_device.bat

# 或手动执行以下命令
adb root
adb shell pm clear com.android.settings
adb shell am start -n com.android.settings/.DeviceInfoSettings
```

### 2. 版本升级和系统部署

**当前版本**: v4.1 (versionCode: 53)

**部署流程**:
```bash
# 使用自动化脚本
.\deploy_to_car.bat

# 或手动执行
gradlew :Launcher:assembleRelease
adb root
adb remount
adb push Launcher\build\outputs\apk\release\YX_Launcher.apk /system/priv-app/YX_Launcher/
adb shell sync
adb reboot
```

### 3. ADB调试功能恢复

**问题描述**: 推送到system分区后ADB调试失效

**解决方案**:
```bash
# 运行ADB修复脚本
.\fix_adb_debug.bat

# 或手动执行关键命令
adb root
adb shell setprop persist.service.adb.enable 1
adb shell setprop persist.service.debuggable 1
adb shell settings put global adb_enabled 1
adb shell settings put global development_settings_enabled 1
```

## 脚本使用指南

### 主调试脚本: debug_weather.bat
- **选项1**: 普通APK构建安装（开发测试用）
- **选项2**: 查看天气相关日志
- **选项3**: 清除应用数据
- **选项4**: 重启Launcher应用
- **选项5**: 检查权限状态
- **选项6**: 测试天气Widget点击事件
- **选项7**: 车机系统部署（推送到system分区）
- **选项8**: 修复ADB调试功能

### 车机部署脚本: deploy_to_car.bat
专门用于车机系统的完整部署流程：
1. 清理和构建Release APK
2. 检查设备连接
3. 获取ROOT权限并重新挂载
4. 推送APK到系统分区
5. 同步文件系统并重启

### ADB修复脚本: fix_adb_debug.bat
多种方法恢复ADB调试功能：
1. 系统属性设置
2. 设置数据库修改
3. 系统配置文件修改
4. 系统服务重启

### 关于本机修复脚本: fix_about_device.bat
修复设置界面问题：
1. 直接启动关于本机界面
2. 清理设置应用缓存
3. 重启系统UI
4. 修复相关权限

## 操作流程建议

### 首次部署
1. 运行 `deploy_to_car.bat` 选择选项1进行完整部署
2. 重启后运行 `fix_adb_debug.bat` 恢复调试功能
3. 如果关于本机界面有问题，运行 `fix_about_device.bat`

### 日常开发调试
1. 使用 `debug_weather.bat` 进行常规开发和测试
2. 需要更新车机时使用选项7进行系统部署
3. 调试功能失效时使用选项8进行修复

### 功能验证
1. 检查天气卡片大小是否与其他widget一致
2. 测试点击天气卡片是否能跳转到详情页
3. 验证24小时预报和7天预报是否正常显示
4. 确认刷新功能是否工作正常

## 注意事项

### 权限要求
- 设备必须已ROOT
- 需要支持 `adb remount` 命令
- 系统分区必须可写

### 版本管理
- 每次更新必须升级versionCode
- 建议同时更新versionName便于识别
- 当前版本: v4.1 (versionCode: 53)

### 调试恢复
- 推送到system分区后开发者选项可能被重置
- 使用提供的脚本可以自动恢复调试功能
- 如果脚本无效，可能需要手动操作或重新刷机

### 备份建议
- 部署前备份原始APK: `adb pull /system/priv-app/YX_Launcher/YX_Launcher.apk`
- 记录原始版本信息以便回滚
- 保留工作正常的APK版本

## 故障排除

### 常见问题
1. **设备连接失败**: 检查USB线缆和驱动程序
2. **ROOT权限获取失败**: 确认设备已正确ROOT
3. **remount失败**: 检查系统分区是否支持重新挂载
4. **推送失败**: 确认目标目录存在且有写权限
5. **重启后功能异常**: 检查APK签名和权限配置

### 日志查看
```bash
# 系统启动日志
adb logcat | grep -i "launcher\|system_server"

# 天气功能日志
adb logcat | grep -i "weather\|yx_weather"

# 权限相关日志
adb logcat | grep -i "permission\|security"
```

---

**更新时间**: 2025-01-29  
**版本**: v4.1  
**适用于**: 车机Android系统
