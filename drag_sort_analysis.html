<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用列表拖拽排序实现分析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 15px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 5px;
        }
        
        .tab-btn {
            padding: 12px 24px;
            margin: 0 5px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .tab-btn.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .tab-content {
            display: none;
            animation: fadeIn 0.5s ease;
        }
        
        .tab-content.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .diagram-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #667eea;
        }
        
        .diagram-title {
            font-size: 1.5em;
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 600;
        }
        
        .flow-diagram {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        
        .flow-step {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 15px 25px;
            border-radius: 50px;
            min-width: 200px;
            text-align: center;
            position: relative;
            font-weight: 500;
            box-shadow: 0 5px 15px rgba(240, 147, 251, 0.4);
            transition: all 0.3s ease;
        }
        
        .flow-step:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.6);
        }
        
        .flow-arrow {
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-top: 20px solid #667eea;
            margin: -5px 0;
        }
        
        .architecture-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .component-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .component-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .component-list {
            list-style: none;
        }
        
        .component-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
        }
        
        .component-list li::before {
            content: "⚡";
            margin-right: 10px;
            font-size: 0.9em;
        }
        
        .sequence-diagram {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin: 30px 0;
            padding: 20px;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            overflow-x: auto;
        }
        
        .actor {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 120px;
        }
        
        .actor-box {
            background: white;
            padding: 15px;
            border-radius: 10px;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .lifeline {
            width: 3px;
            height: 300px;
            background: #667eea;
            position: relative;
        }
        
        .message {
            position: absolute;
            background: #4facfe;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            white-space: nowrap;
            box-shadow: 0 3px 10px rgba(79, 172, 254, 0.4);
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
            position: relative;
        }
        
        .code-block::before {
            content: attr(data-lang);
            position: absolute;
            top: -10px;
            right: 10px;
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.8em;
        }
        
        .highlight {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #ff6b6b;
        }
        
        .interactive-demo {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            max-width: 300px;
            margin: 20px auto;
        }
        
        .demo-item {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            cursor: move;
            transition: all 0.3s ease;
            user-select: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        
        .demo-item:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }
        
        .demo-item.dragging {
            opacity: 0.7;
            transform: rotate(5deg) scale(1.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 应用列表拖拽排序实现分析</h1>
            <p>DynamicGridView 可拖拽网格核心技术解析</p>
        </div>
        
        <div class="tabs">
            <button class="tab-btn active" onclick="showTab('architecture')">系统架构</button>
            <button class="tab-btn" onclick="showTab('flow')">拖拽流程</button>
            <button class="tab-btn" onclick="showTab('sequence')">时序图</button>
            <button class="tab-btn" onclick="showTab('animation')">动画系统</button>
            <button class="tab-btn" onclick="showTab('demo')">交互演示</button>
        </div>
        
        <!-- 系统架构 -->
        <div id="architecture" class="tab-content active">
            <div class="diagram-container">
                <div class="diagram-title">📊 DynamicGridView 系统架构</div>
                <div class="architecture-grid">
                    <div class="component-card">
                        <div class="component-title">🎯 核心组件</div>
                        <ul class="component-list">
                            <li>DynamicGridView (主控制器)</li>
                            <li>GridView基类继承</li>
                            <li>编辑模式管理</li>
                            <li>触摸事件处理</li>
                            <li>状态管理系统</li>
                        </ul>
                    </div>
                    
                    <div class="component-card">
                        <div class="component-title">🔧 拖拽功能模块</div>
                        <ul class="component-list">
                            <li>OnDragListener 接口</li>
                            <li>OnDropListener 接口</li>
                            <li>触摸事件分发</li>
                            <li>拖拽视图创建</li>
                            <li>位置计算逻辑</li>
                        </ul>
                    </div>
                    
                    <div class="component-card">
                        <div class="component-title">🎨 动画效果模块</div>
                        <ul class="component-list">
                            <li>拖拽动画系统</li>
                            <li>位置交换动画</li>
                            <li>缩放动画效果</li>
                            <li>摇摆动画(可选)</li>
                            <li>硬件加速优化</li>
                        </ul>
                    </div>
                    
                    <div class="component-card">
                        <div class="component-title">📱 适配器接口</div>
                        <ul class="component-list">
                            <li>DynamicGridAdapterInterface</li>
                            <li>BaseDynamicGridAdapter</li>
                            <li>YxGridViewAdapter</li>
                            <li>ViewHolder 模式</li>
                            <li>稳定ID支持</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 拖拽流程 -->
        <div id="flow" class="tab-content">
            <div class="diagram-container">
                <div class="diagram-title">🔄 拖拽操作完整流程</div>
                <div class="flow-diagram">
                    <div class="flow-step">👆 用户长按应用图标</div>
                    <div class="flow-arrow"></div>
                    <div class="flow-step">🎯 进入编辑模式 (startEditMode)</div>
                    <div class="flow-arrow"></div>
                    <div class="flow-step">📱 创建拖拽视图 (createDragView)</div>
                    <div class="flow-arrow"></div>
                    <div class="flow-step">🎨 启动视觉效果 (缩放/摇摆)</div>
                    <div class="flow-arrow"></div>
                    <div class="flow-step">👋 开始拖拽 (ACTION_MOVE)</div>
                    <div class="flow-arrow"></div>
                    <div class="flow-step">🔍 检测目标位置 (checkForSwap)</div>
                    <div class="flow-arrow"></div>
                    <div class="flow-step">🔄 执行位置交换 (reorderElements)</div>
                    <div class="flow-arrow"></div>
                    <div class="flow-step">🎬 播放交换动画 (animateReorder)</div>
                    <div class="flow-arrow"></div>
                    <div class="flow-step">💾 数据持久化 (saveGridListData)</div>
                    <div class="flow-arrow"></div>
                    <div class="flow-step">✅ 拖拽完成 (ACTION_UP)</div>
                </div>
            </div>
            
            <div class="highlight">
                <h3>🔑 核心技术点</h3>
                <p><strong>状态管理：</strong>通过 mEditMode 标志位控制编辑状态</p>
                <p><strong>视图跟踪：</strong>mMobileView 和 mMobileItemId 跟踪当前拖拽项</p>
                <p><strong>位置计算：</strong>实时计算触摸位置对应的目标View</p>
                <p><strong>动画优化：</strong>使用ObjectAnimator实现流畅的位置交换效果</p>
            </div>
        </div>
        
        <!-- 时序图 -->
        <div id="sequence" class="tab-content">
            <div class="diagram-container">
                <div class="diagram-title">⏱️ 拖拽操作时序图</div>
                <div class="sequence-diagram">
                    <div class="actor">
                        <div class="actor-box">用户</div>
                        <div class="lifeline">
                            <div class="message" style="top: 20px; left: 50px;">长按</div>
                            <div class="message" style="top: 120px; left: 50px;">拖拽</div>
                            <div class="message" style="top: 220px; left: 50px;">松开</div>
                        </div>
                    </div>
                    
                    <div class="actor">
                        <div class="actor-box">DynamicGridView</div>
                        <div class="lifeline">
                            <div class="message" style="top: 40px; left: -60px;">startEditMode()</div>
                            <div class="message" style="top: 80px; left: -60px;">createDragView()</div>
                            <div class="message" style="top: 140px; left: -60px;">handleDragMove()</div>
                            <div class="message" style="top: 180px; left: -60px;">checkForSwap()</div>
                            <div class="message" style="top: 240px; left: -60px;">handleDragEnd()</div>
                        </div>
                    </div>
                    
                    <div class="actor">
                        <div class="actor-box">Adapter</div>
                        <div class="lifeline">
                            <div class="message" style="top: 160px; left: -50px;">reorderItems()</div>
                            <div class="message" style="top: 200px; left: -60px;">notifyDataSetChanged()</div>
                        </div>
                    </div>
                    
                    <div class="actor">
                        <div class="actor-box">动画系统</div>
                        <div class="lifeline">
                            <div class="message" style="top: 100px; left: -50px;">scale/wobble</div>
                            <div class="message" style="top: 190px; left: -60px;">animateReorder()</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 动画系统 -->
        <div id="animation" class="tab-content">
            <div class="diagram-container">
                <div class="diagram-title">🎨 动画系统架构</div>
                <div class="architecture-grid">
                    <div class="component-card">
                        <div class="component-title">🎯 拖拽开始动画</div>
                        <ul class="component-list">
                            <li>缩放动画 (scaleX/scaleY)</li>
                            <li>摇摆动画 (rotation)</li>
                            <li>视觉反馈增强</li>
                            <li>用户确认进入编辑模式</li>
                        </ul>
                    </div>
                    
                    <div class="component-card">
                        <div class="component-title">🔄 位置交换动画</div>
                        <ul class="component-list">
                            <li>平移动画 (translationX/Y)</li>
                            <li>向前/向后拖拽逻辑</li>
                            <li>行首行末特殊处理</li>
                            <li>AnimatorSet 动画集合</li>
                        </ul>
                    </div>
                    
                    <div class="component-card">
                        <div class="component-title">⚡ 性能优化</div>
                        <ul class="component-list">
                            <li>硬件加速支持</li>
                            <li>动画时长优化 (150ms)</li>
                            <li>内存管理</li>
                            <li>流畅度保证</li>
                        </ul>
                    </div>
                </div>
                
                <div class="code-block" data-lang="Java">
// 位置交换动画核心实现
private void animateReorder(int oldPosition, int newPosition) {
    List&lt;Animator&gt; animations = new LinkedList&lt;&gt;();
    
    // 计算动画方向
    boolean isForward = newPosition > oldPosition;
    
    if (isForward) {
        // 向前拖拽：元素向右/下移动
        for (int pos = oldPosition; pos < newPosition; pos++) {
            View view = getViewForId(getId(pos));
            if ((pos + 1) % getColumnCount() == 0) {
                // 行末元素：移动到下一行行首
                animations.add(createTranslationAnimations(view, 
                    -view.getWidth() * (getColumnCount() - 1), 0,
                    view.getHeight(), 0));
            } else {
                // 普通元素：向右移动一个位置
                animations.add(createTranslationAnimations(view, 
                    view.getWidth(), 0, 0, 0));
            }
        }
    }
    
    // 执行动画集合
    AnimatorSet animatorSet = new AnimatorSet();
    animatorSet.playTogether(animations);
    animatorSet.setDuration(MOVE_DURATION);
    animatorSet.start();
}
                </div>
            </div>
        </div>
        
        <!-- 交互演示 -->
        <div id="demo" class="tab-content">
            <div class="interactive-demo">
                <div class="diagram-title">🎮 拖拽交互演示</div>
                <p>拖拽下方的应用图标体验排序效果</p>
                
                <div class="demo-grid" id="demoGrid">
                    <div class="demo-item" draggable="true">📱 微信</div>
                    <div class="demo-item" draggable="true">🎵 音乐</div>
                    <div class="demo-item" draggable="true">📷 相机</div>
                    <div class="demo-item" draggable="true">🗺️ 地图</div>
                    <div class="demo-item" draggable="true">⚙️ 设置</div>
                    <div class="demo-item" draggable="true">📞 电话</div>
                </div>
                
                <div class="highlight">
                    <h3>🔧 技术特点总结</h3>
                    <p><strong>拖拽交互：</strong>长按启动、实时拖拽、动画反馈</p>
                    <p><strong>性能优化：</strong>ViewHolder模式、稳定ID、硬件加速</p>
                    <p><strong>车机适配：</strong>焦点管理、触控优化、分辨率适配</p>
                    <p><strong>用户体验：</strong>流畅动画、状态管理、持久化存储</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function showTab(tabName) {
            // 隐藏所有tab内容
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.remove('active'));
            
            // 移除所有按钮的active状态
            const buttons = document.querySelectorAll('.tab-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            
            // 显示选中的tab内容
            document.getElementById(tabName).classList.add('active');
            
            // 设置对应按钮为active
            event.target.classList.add('active');
        }
        
        // 拖拽演示功能
        let draggedElement = null;
        
        document.addEventListener('DOMContentLoaded', function() {
            const demoItems = document.querySelectorAll('.demo-item');
            
            demoItems.forEach(item => {
                item.addEventListener('dragstart', function(e) {
                    draggedElement = this;
                    this.classList.add('dragging');
                    e.dataTransfer.effectAllowed = 'move';
                });
                
                item.addEventListener('dragend', function(e) {
                    this.classList.remove('dragging');
                    draggedElement = null;
                });
                
                item.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';
                });
                
                item.addEventListener('drop', function(e) {
                    e.preventDefault();
                    if (draggedElement && draggedElement !== this) {
                        // 交换元素位置
                        const grid = document.getElementById('demoGrid');
                        const draggedIndex = Array.from(grid.children).indexOf(draggedElement);
                        const targetIndex = Array.from(grid.children).indexOf(this);
                        
                        if (draggedIndex < targetIndex) {
                            grid.insertBefore(draggedElement, this.nextSibling);
                        } else {
                            grid.insertBefore(draggedElement, this);
                        }
                        
                        // 添加交换动画效果
                        draggedElement.style.transform = 'scale(1.1)';
                        this.style.transform = 'scale(1.1)';
                        
                        setTimeout(() => {
                            draggedElement.style.transform = '';
                            this.style.transform = '';
                        }, 200);
                    }
                });
            });
        });
    </script>
</body>
</html>